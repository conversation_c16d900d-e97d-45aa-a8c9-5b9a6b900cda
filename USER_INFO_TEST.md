# 用户信息获取功能测试指南

## 功能概述

在扫码登录成功后，系统会自动尝试获取登录用户的基本信息，包括：
- 用户昵称
- 用户头像
- 用户ID

## 测试步骤

### 1. 启动应用
```bash
npm run dev
```

### 2. 进入账号管理页面
- 打开应用后，点击"账号管理"标签页

### 3. 添加抖音账号
- 点击右上角的"授权账号"按钮
- 在弹出的对话框中选择"抖音"图标
- 系统会打开一个新的浏览器窗口，导航到抖音创作者平台

### 4. 完成扫码登录
- 在打开的浏览器窗口中，使用手机抖音App扫描二维码
- 在手机上确认登录
- 等待页面完全加载（通常需要3-5秒）

### 5. 保存登录信息
- 在登录窗口右上角会显示"页面已暂停"的提示
- 点击"保存状态"按钮
- 系统会自动获取用户信息并保存

### 6. 验证用户信息
- 登录窗口会自动关闭
- 在账号管理页面中，抖音账号列表会显示新添加的账号
- 检查是否显示了用户头像、昵称和用户ID

## 预期结果

### 成功情况
- 账号列表中显示用户头像（圆形图片）
- 显示用户昵称而不是"抖音账号 xxx"
- 显示用户ID（如果获取到）
- 显示登录时间

### 失败情况
- 显示"未知用户"作为昵称
- 没有头像显示
- 显示"未获取到用户头像"的提示

## 调试方法

### 1. 查看控制台日志
- 在应用中按F12打开开发者工具
- 查看Console标签页
- 寻找以下日志信息：
  - "开始获取用户信息..."
  - "获取到的用户信息: {nickname, avatar, userId}"
  - "用户信息获取结果: ..."

### 2. 使用测试按钮
- 在账号管理页面中，点击"查看所有账号信息"按钮
- 查看控制台输出的详细信息
- 检查userInfo字段是否包含正确的数据

### 3. 检查文件数据
```bash
# 查看保存的登录信息文件
cat data/douyin.json
```

## 常见问题

### 1. 获取不到用户信息
**可能原因**：
- 页面结构发生变化
- 用户信息存储位置改变
- 网络连接问题

**解决方案**：
- 检查控制台日志，查看具体的错误信息
- 尝试重新登录
- 确认登录是否真正成功

### 2. 头像显示失败
**可能原因**：
- 头像URL无效
- 跨域访问限制
- 网络连接问题

**解决方案**：
- 检查头像URL是否可访问
- 查看控制台是否有跨域错误
- 尝试在浏览器中直接访问头像URL

### 3. 昵称显示为"未知用户"
**可能原因**：
- 页面元素选择器不匹配
- 用户信息未正确加载
- 页面结构发生变化

**解决方案**：
- 检查页面是否完全加载
- 查看控制台日志中的获取过程
- 尝试等待更长时间再保存

## 技术细节

### 获取方式
系统采用多种方式尝试获取用户信息：

1. **页面元素解析**
   ```javascript
   const nicknameElement = document.querySelector('[data-e2e="user-nickname"], .user-nickname, .nickname, [class*="nickname"], [class*="user"]');
   const avatarElement = document.querySelector('[data-e2e="user-avatar"], .user-avatar, .avatar, [class*="avatar"], img[src*="avatar"]');
   ```

2. **本地存储读取**
   ```javascript
   const userInfoFromStorage = localStorage.getItem('userInfo') || localStorage.getItem('user_info') || localStorage.getItem('douyin_user_info');
   ```

3. **URL参数解析**
   ```javascript
   const userIdFromUrl = window.location.pathname.match(/\/user\/([^\/]+)/)?.[1] || window.location.search.match(/user_id=([^&]+)/)?.[1];
   ```

4. **脚本标签解析**
   ```javascript
   const scripts = document.querySelectorAll('script');
   // 在脚本内容中查找用户信息
   ```

### 数据结构
用户信息存储在以下结构中：
```json
{
  "accounts": {
    "accountId": {
      "cookies": [...],
      "localStorage": {...},
      "sessionStorage": {...},
      "userAgent": "...",
      "timestamp": **********,
      "userInfo": {
        "nickname": "用户昵称",
        "avatar": "头像URL",
        "userId": "用户ID"
      }
    }
  },
  "lastUpdated": **********
}
```

## 扩展测试

### 测试其他平台
同样的测试方法也适用于快手和小红书平台：

1. **快手平台**
   - 选择快手图标进行登录
   - 完成扫码登录流程
   - 检查用户信息获取结果

2. **小红书平台**
   - 选择小红书图标进行登录
   - 完成扫码登录流程
   - 检查用户信息获取结果

### 多账号测试
- 添加多个同一平台的账号
- 验证每个账号的用户信息是否正确获取
- 检查账号列表显示是否正常

## 性能测试

### 获取速度
- 记录从点击"保存状态"到获取完成的时间
- 正常情况下应该在1-3秒内完成

### 内存使用
- 监控应用内存使用情况
- 确保用户信息获取不会导致内存泄漏

### 错误处理
- 测试网络断开情况下的表现
- 测试页面加载失败时的处理
- 验证错误不会影响登录流程

## 总结

用户信息获取功能通过多种方式尝试获取用户数据，提高了获取成功率。即使某些方式失败，也不会影响正常的登录流程。通过详细的日志输出和测试工具，可以方便地调试和验证功能是否正常工作。 