// See the Electron documentation for details on how to use preload scripts:
// https://www.electronjs.org/docs/latest/tutorial/process-model#preload-scripts

import { contextBridge, ipc<PERSON><PERSON><PERSON> } from "electron";

// --------- Expose some API to the Renderer process ---------
contextBridge.exposeInMainWorld("electronAPI", {
  getUserBind: () => ipcRenderer.invoke("get-user-bind"),
  createPublishTask: (task: FormData) => ipcRenderer.invoke("create-publish-task", task),
  bindUser: (data: any) => ipcRenderer.invoke("bind-user", data),
  bindSingleAccount: (data: any) => ipcRenderer.invoke("bind-single-account", data),
  getUserTasks: (userId: string) => ipcRenderer.invoke("get-user-tasks", userId),
  login: (data: { en_user_name: string; p: string }) => ipcRenderer.invoke("login", data),
  logout: () => ipcRenderer.invoke("logout"),

  // Token 相关操作
  token: {
    set: (token: string, rememberMe: boolean) => ipcR<PERSON>er.invoke("token:set", token, rememberMe),
    get: () => ipcRenderer.invoke("token:get"),
  },

  // 抖音登录相关
  douyin: {
    startLogin: (accountId?: string) => ipcRenderer.invoke("douyin:start-login", accountId),
    hasLoginInfo: () => ipcRenderer.invoke("douyin:has-login-info"),
    hasAccountLoginInfo: (accountId: string) => ipcRenderer.invoke("douyin:has-account-login-info", accountId),
    getAllAccountsData: () => ipcRenderer.invoke("douyin:get-all-accounts-data"),
    getAccountLoginInfo: (accountId: string) => ipcRenderer.invoke("douyin:get-account-login-info", accountId),
    getLoginInfo: () => ipcRenderer.invoke("douyin:get-login-info"),
    deleteAccountLoginInfo: (accountId: string) => ipcRenderer.invoke("douyin:delete-account-login-info", accountId),
    getLoginFilePath: () => ipcRenderer.invoke("douyin:get-login-file-path"),
    openAccount: (accountId: string) => ipcRenderer.invoke("douyin:open-account", accountId),
    onLoginCompleted: (callback: (data: any) => void) => {
      ipcRenderer.on("douyin:login-completed", (_, data) => callback(data));
    },
  },
  // 快手登录相关
  kuaishou: {
    startLogin: (accountId?: string) => ipcRenderer.invoke("kuaishou:start-login", accountId),
    hasLoginInfo: () => ipcRenderer.invoke("kuaishou:has-login-info"),
    hasAccountLoginInfo: (accountId: string) => ipcRenderer.invoke("kuaishou:has-account-login-info", accountId),
    getAllAccountsData: () => ipcRenderer.invoke("kuaishou:get-all-accounts-data"),
    getAccountLoginInfo: (accountId: string) => ipcRenderer.invoke("kuaishou:get-account-login-info", accountId),
    getLoginInfo: () => ipcRenderer.invoke("kuaishou:get-login-info"),
    deleteAccountLoginInfo: (accountId: string) => ipcRenderer.invoke("kuaishou:delete-account-login-info", accountId),
    getLoginFilePath: () => ipcRenderer.invoke("kuaishou:get-login-file-path"),
    openAccount: (accountId: string) => ipcRenderer.invoke("kuaishou:open-account", accountId),
    createSingleAccountFileAndBind: (accountId: string, platform: string, userId?: string, enUserName?: string) =>
      ipcRenderer.invoke("kuaishou:create-single-account-file-and-bind", accountId, platform, userId, enUserName),
    onLoginCompleted: (callback: (data: any) => void) => {
      ipcRenderer.on("kuaishou:login-completed", (_, data) => callback(data));
    },
  },
  // 小红书登录相关
  xiaohongshu: {
    startLogin: () => ipcRenderer.invoke("xiaohongshu:start-login"),
    hasLoginInfo: () => ipcRenderer.invoke("xiaohongshu:has-login-info"),
    getLoginInfo: () => ipcRenderer.invoke("xiaohongshu:get-login-info"),
  },
  // 视频号登录相关
  shipinhao: {
    startLogin: (accountId?: string) => ipcRenderer.invoke("shipinhao:start-login", accountId),
    hasLoginInfo: () => ipcRenderer.invoke("shipinhao:has-login-info"),
    hasAccountLoginInfo: (accountId: string) => ipcRenderer.invoke("shipinhao:has-account-login-info", accountId),
    onLoginCompleted: (callback: (data: any) => void) => {
      ipcRenderer.on("shipinhao:login-completed", (_, data) => callback(data));
    },
  },
  // 哔哩哔哩登录相关
  bilibili: {
    startLogin: (accountId?: string) => ipcRenderer.invoke("bilibili:start-login", accountId),
    hasLoginInfo: () => ipcRenderer.invoke("bilibili:has-login-info"),
    hasAccountLoginInfo: (accountId: string) => ipcRenderer.invoke("bilibili:has-account-login-info", accountId),
    onLoginCompleted: (callback: (data: any) => void) => {
      ipcRenderer.on("bilibili:login-completed", (_, data) => callback(data));
    },
  },
});

// --------- Preload scripts are loaded before every page load ---------
// You can expose APIs to the renderer process here.
