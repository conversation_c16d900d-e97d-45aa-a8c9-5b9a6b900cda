import { create } from "zustand";

export type UserInfo = { id: number; en_user_name: string; user_nick_name: string };

interface LoginStore {
  isLogin: boolean;
  userInfo: UserInfo | null;
  setIsLogin: (isLogin: boolean) => void;
  setUserInfo: (userInfo: UserInfo) => void;
}

export const useLoginStore = create<LoginStore>((set) => ({
  isLogin: false,
  userInfo: null,
  setIsLogin: (isLogin) => set({ isLogin }),
  setUserInfo: (userInfo) => set({ userInfo }),
}));
