import { createStore, useStore } from "zustand";
import { combine } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import type { AccountInfo, AccountStore } from "@/types/accountType";

const accountStore = createStore<AccountStore>()(
  immer(
    combine(
      {
        accounts: [] as AccountInfo["accounts"],
        isLoginDialogOpen: false,
      },
      (set, get) => ({
        setAccountInfo: (accountInfo) =>
          set((state) => {
            state.accounts = accountInfo;
          }),
        setIsLoginDialogOpen: (isLoginDialogOpen) =>
          set((state) => {
            state.isLoginDialogOpen = isLoginDialogOpen;
          }),
        isAuthenticated: () => {
          const state = get();
          return state.accounts && state.accounts.length > 0;
        },
      }),
    ),
  ),
);

const useAccountStore = () => {
  return useStore(accountStore);
};

export default useAccountStore;
