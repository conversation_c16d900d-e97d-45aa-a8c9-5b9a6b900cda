import { LockOutlined, UserOutlined } from "@ant-design/icons";
import { useNavigate } from "@tanstack/react-router";
import { Button, Card, Form, Input, message, Space, Typography } from "antd";
import type React from "react";
import { useState } from "react";
import { useLoginStore } from "@/store/loginStore";

const { Title, Text } = Typography;

interface LoginForm {
  username: string;
  password: string;
}

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const { setIsLogin, setUserInfo } = useLoginStore();
  const navigate = useNavigate();

  const onFinish = async (values: LoginForm) => {
    setLoading(true);
    try {
      // 这里可以添加实际的登录逻辑
      const { success, data } = await window.electronAPI.login({
        en_user_name: values.username,
        p: values.password,
      });
      console.log("loginResult: ", success, data);
      if (success) {
        setIsLogin(true);
        setUserInfo(data!);
        // 登录成功后跳转到账号页面
        navigate({ to: "/" });
      }
    } catch (error) {
      console.error("登录失败:", error);
      message.error("用户名或密码错误");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      }}
    >
      <Card
        style={{
          width: 400,
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
          borderRadius: 12,
        }}
      >
        <Space direction="vertical" size="large" style={{ width: "100%" }}>
          <div style={{ textAlign: "center" }}>
            <Title level={2} style={{ margin: 0, color: "#1890ff" }}>
              媒体平台登录
            </Title>
            <Text type="secondary">请输入您的账号信息</Text>
          </div>

          <Form name="login" onFinish={onFinish} autoComplete="off" layout="vertical">
            <Form.Item name="username" rules={[{ required: true, message: "请输入用户名!" }]}>
              <Input prefix={<UserOutlined />} placeholder="用户名" size="large" />
            </Form.Item>

            <Form.Item name="password" rules={[{ required: true, message: "请输入密码!" }]}>
              <Input.Password prefix={<LockOutlined />} placeholder="密码" size="large" />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} size="large" style={{ width: "100%" }}>
                登录
              </Button>
            </Form.Item>
          </Form>
        </Space>
      </Card>
    </div>
  );
};

export default Login;
