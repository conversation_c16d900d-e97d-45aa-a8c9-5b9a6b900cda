import {
  Bar<PERSON>hartOutlined,
  LikeOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  ShareAltOutlined,
  UserOutlined,
  <PERSON>CameraOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Col,
  Progress,
  Row,
  Space,
  Statistic,
  Typography,
} from "antd";
import React from "react";

const { Title, Paragraph } = Typography;

export default function Dashboard() {
  return (
    <div className="p-6">
      <Title level={2}>媒体平台仪表板</Title>
      <Paragraph>欢迎使用您的媒体管理平台</Paragraph>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总账号数"
              value={12}
              prefix={<UserOutlined />}
              valueStyle={{ color: "#3f8600" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="视频数量"
              value={156}
              prefix={<VideoCameraOutlined />}
              valueStyle={{ color: "#1890ff" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总播放量"
              value={89234}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: "#722ed1" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总点赞数"
              value={23456}
              prefix={<LikeOutlined />}
              valueStyle={{ color: "#eb2f96" }}
            />
          </Card>
        </Col>
      </Row>

      {/* 功能卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="快速操作" extra={<SettingOutlined />}>
            <Space direction="vertical" style={{ width: "100%" }}>
              <Button type="primary" block icon={<VideoCameraOutlined />}>
                发布视频
              </Button>
              <Button block icon={<BarChartOutlined />}>
                查看数据
              </Button>
              <Button block icon={<UserOutlined />}>
                管理账号
              </Button>
            </Space>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="平台进度" extra={<ShareAltOutlined />}>
            <Space direction="vertical" style={{ width: "100%" }}>
              <div>
                <div className="flex justify-between mb-2">
                  <span>抖音</span>
                  <span>85%</span>
                </div>
                <Progress percent={85} status="active" />
              </div>
              <div>
                <div className="flex justify-between mb-2">
                  <span>快手</span>
                  <span>72%</span>
                </div>
                <Progress percent={72} status="active" />
              </div>
              <div>
                <div className="flex justify-between mb-2">
                  <span>小红书</span>
                  <span>93%</span>
                </div>
                <Progress percent={93} status="active" />
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
}
