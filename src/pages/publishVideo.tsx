import type { GetProp, UploadProps } from "antd";
import { Button, Form, Input, message, Select, Upload } from "antd";
import { ChevronDown, Upload as UploadIcon } from "lucide-react";
import { useId, useState } from "react";
import useAccountStore from "@/store/accountStore";
import { useLoginStore } from "@/store/loginStore";
import { ContentTypeEnum } from "@/types/accountType";

const { TextArea } = Input;
type FileType = Parameters<GetProp<UploadProps, "beforeUpload">>[0];

function getBase64(file: FileType): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });
}

// 文件转ArrayBuffer的工具函数
function fileToArrayBuffer(file: File): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as ArrayBuffer);
    reader.onerror = reject;
    reader.readAsArrayBuffer(file);
  });
}

export default function PublishVideoPage() {
  const { userInfo } = useLoginStore();
  const { accounts } = useAccountStore();
  const descriptionId = useId();

  const [form] = Form.useForm();
  const [videoPreview, setVideoPreview] = useState<string | null>(null);
  const [coverImage, setCoverImage] = useState<string | null>(null);

  const onFinish = async (values: any) => {
    const videoFile = values.video_files;
    console.log("Form values:", values);
    message.success("表单提交成功！");

    try {
      // 转换文件为ArrayBuffer
      const videoBuffer = videoFile?.[0]?.originFileObj ? await fileToArrayBuffer(videoFile[0].originFileObj) : null;
      const imageBuffer = values.image_files?.[0]?.originFileObj ? await fileToArrayBuffer(values.image_files[0].originFileObj) : null;

      // 创建要发送的数据对象
      const taskData = {
        video_files: videoBuffer ? Array.from(new Uint8Array(videoBuffer)) : null,
        image_files: imageBuffer ? Array.from(new Uint8Array(imageBuffer)) : null,
        bind_ids: values.bind_ids.join(","),
        title: values.title ? values.title : "我是标题",
        topics: values.topic_ids,
        description: values.description,
        content_type: ContentTypeEnum.Video,
        user_id: userInfo?.id,
        en_user_name: userInfo?.en_user_name,
      };

      const res = await window.electronAPI.createPublishTask(taskData);
      console.log("res: ", res);
    } catch (error) {
      console.error("create publish task error: ", error);
      message.error("发布任务创建失败");
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log("Form validation failed:", errorInfo);
    message.error("请检查表单信息");
  };

  const beforeUpload = (file: File) => {
    const isMP4 = file.type === "video/mp4";
    if (!isMP4) {
      message.error("只能上传 MP4 格式的视频文件！");
      return false;
    }

    const isLt500M = file.size / 1024 / 1024 < 500;
    if (!isLt500M) {
      message.error("视频文件大小不能超过 500MB！");
      return false;
    }

    return false; // 阻止自动上传，手动处理
  };

  const handleVideoChange: UploadProps["onChange"] = (info) => {
    console.log("handleVideoChange: ", info);
    if (info.file.status === "removed") {
      form.setFieldsValue({ video_files: undefined });
      setVideoPreview(null);
    } else {
      // 设置文件到正确的表单字段
      form.setFieldsValue({ video_files: info.fileList });
      // 生成视频预览
      if (info.fileList.length > 0 && info.fileList[0].originFileObj) {
        generateVideoPreview(info.fileList[0].originFileObj as FileType);
      }
    }
  };

  const handleCoverImageChange: UploadProps["onChange"] = async (info) => {
    console.log("handleCoverImageChange: ", info);
    if (info.file.status === "removed") {
      form.setFieldsValue({ image_files: undefined });
      setCoverImage(null);
    } else {
      // 设置文件到正确的表单字段
      form.setFieldsValue({ image_files: info.fileList });
      // 生成预览
      if (info.fileList.length > 0 && info.fileList[0].originFileObj) {
        const base64 = await getBase64(info.fileList[0].originFileObj as FileType);
        setCoverImage(base64);
      }
    }
  };

  const generateVideoPreview = (file: FileType) => {
    console.log("file: ", file);
    const video = document.createElement("video");
    video.muted = true;
    video.crossOrigin = "anonymous";
    video.preload = "metadata";

    video.onloadedmetadata = () => {
      console.log("Video metadata loaded, dimensions:", video.videoWidth, "x", video.videoHeight);
      // 视频元数据加载完成后，设置到第一帧
      video.currentTime = 0;
    };

    video.onseeked = () => {
      console.log("Video seeked to first frame");
      // 创建 canvas 来捕获第一帧
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");

      if (ctx) {
        // 设置 canvas 尺寸为视频尺寸
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        console.log("Canvas dimensions:", canvas.width, "x", canvas.height);

        // 绘制视频帧到 canvas
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        // 将 canvas 转换为 base64 图片
        const previewUrl = canvas.toDataURL("image/jpeg", 0.8);
        console.log("Preview URL generated:", `${previewUrl.substring(0, 50)}...`);
        setVideoPreview(previewUrl);

        // 清理资源
        URL.revokeObjectURL(video.src);
      }
    };

    video.onerror = (e) => {
      console.error("Video error:", e);
      message.error("视频预览生成失败");
    };

    // 设置视频源
    try {
      const blobUrl = URL.createObjectURL(file);
      console.log("Blob URL created:", blobUrl);
      video.src = blobUrl;
    } catch (error) {
      message.error("视频预览生成失败");
      console.error("Video preview error:", error);
    }
  };

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <header className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">发布视频</h1>
        <Button onClick={() => form.submit()}>
          一键发布
          <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </header>

      <Form form={form} layout="vertical" onFinish={onFinish} onFinishFailed={onFinishFailed}>
        <Form.Item name="video_files" label="视频" rules={[{ required: true, message: "请选择视频" }]} labelCol={{ span: 24 }} wrapperCol={{ span: 24 }}>
          <div className="flex gap-4">
            <Upload beforeUpload={beforeUpload} onChange={handleVideoChange} accept=".mp4" maxCount={1} className="w-48">
              <div className="w-48 h-60 border-2 border-dashed rounded-md flex flex-col justify-center items-center cursor-pointer hover:border-primary bg-gray-50">
                <UploadIcon className="w-8 h-8 text-gray-400" />
                <span className="mt-2 text-gray-500">上传 MP4 视频</span>
                <span className="text-xs text-gray-400 mt-1">最大 500MB</span>
              </div>
            </Upload>

            {/* 视频预览 */}
            {videoPreview && (
              <div className="w-48 h-60 bg-black rounded-md overflow-hidden relative">
                <img src={videoPreview} alt="视频首帧预览" className="w-full h-full object-cover" />
                <div className="absolute top-2 left-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">首帧预览</div>
              </div>
            )}
          </div>
        </Form.Item>

        {/* Account Selection */}
        <Form.Item name="bind_ids" label="账号" rules={[{ required: true, message: "请添加至少一个账号" }]} labelCol={{ span: 24 }} wrapperCol={{ span: 24 }}>
          <Select
            mode="multiple"
            options={accounts.map((account) => ({
              label: account.platform + account.uuid,
              value: account.id,
            }))}
            placeholder="请选择账号"
          ></Select>
        </Form.Item>

        {/* Cover */}
        <Form.Item name="image_files" label="封面" rules={[{ required: false, message: "请选择封面" }]} labelCol={{ span: 24 }} wrapperCol={{ span: 24 }}>
          <div className="flex gap-4">
            <Upload beforeUpload={() => false} onChange={handleCoverImageChange} accept=".jpg,.jpeg,.png" maxCount={1} className="w-48">
              <div className="w-48 h-60 border-2 border-dashed rounded-md flex flex-col justify-center items-center cursor-pointer hover:border-primary bg-gray-50">
                <UploadIcon className="w-8 h-8 text-gray-400" />
                <span className="mt-2 text-gray-500">上传封面</span>
              </div>
            </Upload>
            {coverImage && (
              <div className="w-48 h-60 bg-black rounded-md overflow-hidden relative">
                <img src={coverImage} alt="封面" className="w-full h-full object-cover" />
                <div className="absolute top-2 left-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">封面</div>
              </div>
            )}
          </div>
        </Form.Item>

        <div>
          <div className="flex items-center mb-4">
            <h2 className="font-semibold">基础信息</h2>
          </div>
          <Form.Item name="title" label="标题" rules={[{ required: true, message: "请输入标题" }]}>
            <Input placeholder="请输入标题" />
          </Form.Item>
          <Form.Item name="description" label="描述" rules={[{ max: 500, message: "描述不能超过500字符" }]} labelCol={{ span: 24 }} wrapperCol={{ span: 24 }}>
            <TextArea id={descriptionId} placeholder="请输入描述..." className="min-h-[120px]" showCount maxLength={500} />
          </Form.Item>
          <div className="mt-4 flex gap-2">
            <Button>#添加话题</Button>
            <Button>常用话题</Button>
          </div>
        </div>
      </Form>
    </div>
  );
}
