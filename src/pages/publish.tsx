import { useNavigate } from "@tanstack/react-router";
import { Button, Dropdown, Select, Table } from "antd";
import dayjs from "dayjs";
import { Plus, RefreshCw } from "lucide-react";
import { useEffect, useState } from "react";
import EmptyList from "@/components/emptyList";
import { PlatformEnum } from "@/types/accountType";
import type { TaskListItem } from "@/types/task";

const platformMap = {
  [PlatformEnum.DouYinWeb]: "抖音",
  [PlatformEnum.KuaiShouWeb]: "快手",
  [PlatformEnum.ShiPinHaoWeb]: "视频号",
  [PlatformEnum.WeiBoWeb]: "微博",
  [PlatformEnum.XiaoHongShuWebCreator]: "小红书",
  [PlatformEnum.XiaoHongShuWebWWW]: "小红书",
  [PlatformEnum.BiliBiliWeb]: "B站",
  [PlatformEnum.TouTiaoWeb]: "头条",
};

export default function Publish() {
  const [taskList, setTaskList] = useState<TaskListItem[]>([]);
  const navigate = useNavigate();

  const columns = [
    {
      title: "发布人",
      dataIndex: "en_user_name",
    },
    {
      title: "发布平台",
      dataIndex: "bind_list",
      render: (text: { platform: PlatformEnum }[]) => {
        return text.map((item) => platformMap[item.platform]).join(",");
      },
    },
    {
      title: "标题",
      dataIndex: "title",
    },
    {
      title: "发布类型",
      dataIndex: "content_type",
      render: (text: string) => {
        return text === "VIDEO" ? "视频" : "图文";
      },
    },
    {
      title: "发布时间",
      dataIndex: "exe_time",
      render: (text: string) => {
        return dayjs(text).format("YYYY-MM-DD HH:mm:ss");
      },
    },
    {
      title: "发布状态",
      dataIndex: "exe_status",
    },
  ];
  const handleMenuClick = ({ key }: { key: string }) => {
    console.log("menu click: ", key);
    navigate({ to: "/publishVideo", params: { type: key } });
  };

  useEffect(() => {
    const getUserTasks = async () => {
      const userTasks = await window.electronAPI.getUserTasks("37");
      console.log("userTasks", userTasks);
      setTaskList(userTasks);
    };
    getUserTasks();
  }, []);

  return (
    <div className="h-full w-full bg-white p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-2">
          <h1 className="text-lg font-semibold">发布记录</h1>
          <Button type="text" icon={<RefreshCw className="h-4 w-4" />} />
        </div>
      </div>

      <div className="mt-4 flex items-center">
        <div className="flex items-center gap-x-2">
          <Select
            placeholder="全部发布人"
            style={{ width: 180 }}
            options={[
              { value: "light", label: "Light" },
              { value: "dark", label: "Dark" },
              { value: "system", label: "System" },
            ]}
          />
          <Select
            placeholder="全部发布类型"
            style={{ width: 180 }}
            options={[
              { value: "light", label: "Light" },
              { value: "dark", label: "Dark" },
              { value: "system", label: "System" },
            ]}
          />
          <Select
            placeholder="全部推送状态"
            style={{ width: 180 }}
            options={[
              { value: "light", label: "Light" },
              { value: "dark", label: "Dark" },
              { value: "system", label: "System" },
            ]}
          />
          <Button>导出</Button>
          <Button>批量删除</Button>
        </div>
        <div className="ml-auto">
          <Dropdown
            menu={{
              items: [
                { key: "video", label: "视频" },
                { key: "image", label: "图文" },
              ],
              onClick: handleMenuClick,
            }}
          >
            <Button type="primary">
              <Plus className="mr-2 h-4 w-4" />
              新增发布
            </Button>
          </Dropdown>
        </div>
      </div>
      {taskList.length > 0 ? (
        <div className="mt-4">
          <Table rowKey="id" dataSource={taskList} columns={columns} pagination={false} bordered />
        </div>
      ) : (
        <div className="mt-20 flex flex-col items-center justify-center">
          <EmptyList />
        </div>
      )}
    </div>
  );
}
