import type { GetProp, UploadProps } from "antd";
import { Button, Form, Input, message, Select, Upload } from "antd";
import { ChevronDown, Upload as UploadIcon } from "lucide-react";
import { useId, useState } from "react";
import useAccountStore from "@/store/accountStore";
import { useLoginStore } from "@/store/loginStore";
import { ContentTypeEnum } from "@/types/accountType";

const { TextArea } = Input;
type FileType = Parameters<GetProp<UploadProps, "beforeUpload">>[0];

function getBase64(file: FileType): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });
}

// 文件转ArrayBuffer的工具函数
function fileToArrayBuffer(file: File): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as ArrayBuffer);
    reader.onerror = reject;
    reader.readAsArrayBuffer(file);
  });
}

export default function PublishImagePage() {
  const { userInfo } = useLoginStore();
  const { accounts } = useAccountStore();
  const descriptionId = useId();

  const [form] = Form.useForm();
  const [coverImage, setCoverImage] = useState<string | null>(null);

  const onFinish = async (values: any) => {
    const videoFile = values.video_files;
    console.log("Form values:", values);
    message.success("表单提交成功！");

    try {
      // 转换文件为ArrayBuffer
      const videoBuffer = videoFile?.[0]?.originFileObj ? await fileToArrayBuffer(videoFile[0].originFileObj) : null;
      const imageBuffer = values.image_files?.[0]?.originFileObj ? await fileToArrayBuffer(values.image_files[0].originFileObj) : null;

      // 创建要发送的数据对象
      const taskData = {
        video_files: videoBuffer ? Array.from(new Uint8Array(videoBuffer)) : null,
        image_files: imageBuffer ? Array.from(new Uint8Array(imageBuffer)) : null,
        bind_ids: values.bind_ids.join(","),
        title: values.title ? values.title : "我是标题",
        topics: values.topic_ids,
        description: values.description,
        content_type: ContentTypeEnum.Video,
        user_id: userInfo?.id,
        en_user_name: userInfo?.en_user_name,
      };

      const res = await window.electronAPI.createPublishTask(taskData);
      console.log("res: ", res);
    } catch (error) {
      console.error("create publish task error: ", error);
      message.error("发布任务创建失败");
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log("Form validation failed:", errorInfo);
    message.error("请检查表单信息");
  };

  const handleCoverImageChange: UploadProps["onChange"] = async (info) => {
    console.log("handleCoverImageChange: ", info);
    if (info.file.status === "removed") {
      form.setFieldsValue({ image_files: undefined });
      setCoverImage(null);
    } else {
      // 设置文件到正确的表单字段
      form.setFieldsValue({ image_files: info.fileList });
      // 生成预览
      if (info.fileList.length > 0 && info.fileList[0].originFileObj) {
        const base64 = await getBase64(info.fileList[0].originFileObj as FileType);
        setCoverImage(base64);
      }
    }
  };

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <header className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">发布图文</h1>
        <Button onClick={() => form.submit()}>
          一键发布
          <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </header>
      {/* Cover */}
      <Form.Item name="image_files" label="图片" rules={[{ required: false, message: "请选择封面" }]} labelCol={{ span: 24 }} wrapperCol={{ span: 24 }}>
        <div className="flex gap-4">
          <Upload beforeUpload={() => false} onChange={handleCoverImageChange} accept=".jpg,.jpeg,.png" maxCount={1} className="w-48">
            <div className="w-48 h-60 border-2 border-dashed rounded-md flex flex-col justify-center items-center cursor-pointer hover:border-primary bg-gray-50">
              <UploadIcon className="w-8 h-8 text-gray-400" />
              <span className="mt-2 text-gray-500">上传图片</span>
            </div>
          </Upload>
          {coverImage && (
            <div className="w-48 h-60 bg-black rounded-md overflow-hidden relative">
              <img src={coverImage} alt="封面" className="w-full h-full object-cover" />
              <div className="absolute top-2 left-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">封面</div>
            </div>
          )}
        </div>
      </Form.Item>

      <Form form={form} layout="vertical" onFinish={onFinish} onFinishFailed={onFinishFailed}>
        {/* Account Selection */}
        <Form.Item name="bind_ids" label="账号" rules={[{ required: true, message: "请添加至少一个账号" }]} labelCol={{ span: 24 }} wrapperCol={{ span: 24 }}>
          <Select
            mode="multiple"
            options={accounts.map((account) => ({
              label: account.platform + account.uuid,
              value: account.id,
            }))}
            placeholder="请选择账号"
          ></Select>
        </Form.Item>

        <div>
          <div className="flex items-center mb-4">
            <h2 className="font-semibold">基础信息</h2>
          </div>
          <Form.Item name="title" label="标题" rules={[{ required: true, message: "请输入标题" }]}>
            <Input placeholder="请输入标题" />
          </Form.Item>
          <Form.Item name="description" label="描述" rules={[{ max: 500, message: "描述不能超过500字符" }]} labelCol={{ span: 24 }} wrapperCol={{ span: 24 }}>
            <TextArea id={descriptionId} placeholder="请输入描述..." className="min-h-[120px]" showCount maxLength={500} />
          </Form.Item>
          <div className="mt-4 flex gap-2">
            <Button>#添加话题</Button>
            <Button>常用话题</Button>
          </div>
        </div>
      </Form>
    </div>
  );
}
