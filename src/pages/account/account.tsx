import { But<PERSON>, Card, Input, Select } from "antd";
import { Plus } from "lucide-react";
import useAccountStore from "@/store/accountStore";

const accountItems = [
  {
    label: "全部账号",
    value: "all",
  },
  {
    label: "正常",
    value: "normal",
  },
  {
    label: "失效",
    value: "invalid",
  },
  {
    label: "未实名",
    value: "unreal",
  },
];

export default function Account() {
  const { accounts, setIsLoginDialogOpen } = useAccountStore();

  return (
    <div className="flex flex-col gap-3 md:gap-4">
      {/* 无失效账号提示 */}
      <div className="flex items-center gap-2 text-xs md:text-sm text-muted-foreground bg-accent/50 p-2 rounded">
        <span>?</span>
        <span>无失效账号</span>
      </div>

      {/* 筛选和搜索栏 */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4">
        <div className="flex items-center gap-2 md:gap-4 overflow-x-auto pb-1 sm:pb-0 scrollbar-none">
          <Select
            placeholder="全部账号"
            style={{ width: 120 }}
            options={accountItems}
          />

          <Select
            placeholder="全部分组"
            style={{ width: 120 }}
            options={[
              { value: "all", label: "全部分组" },
              { value: "default", label: "默认分组" },
            ]}
          />

          <Select
            placeholder="全部平台"
            style={{ width: 120 }}
            options={[
              { value: "all", label: "全部平台" },
              { value: "tiktok", label: "TikTok" },
              { value: "douyin", label: "抖音" },
            ]}
          />
        </div>

        <div className="flex gap-2 items-center flex-1 max-w-80">
          <div className="relative flex-1 max-w-80 min-w-20">
            <Input
              type="text"
              placeholder="输入账号、地址搜索"
              className="pr-8"
            />
            <span className="absolute right-3 top-1/2 -translate-y-1/2 opacity-40">
              🔍
            </span>
          </div>
        </div>

        <Button
          type="primary"
          className="shrink-0 ml-auto"
          icon={<Plus className="size-4" />}
          onClick={() => setIsLoginDialogOpen(true)}
        >
          授权账号
        </Button>
      </div>

      {/* 账号列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 md:gap-4">
        {accounts.map((account) => {
          return (
            <Card className="border-0 shadow-sm w-64 h-32" key={account.id}>
              <div className="flex items-center gap-3 p-3 md:p-4">
                <div className="flex flex-col gap-0.5 md:gap-1 min-w-0">
                  <div className="font-medium truncate">
                    {account.status_reason}
                  </div>
                  <div className="text-xs md:text-sm text-muted-foreground">
                    {account.platform}
                  </div>
                </div>
              </div>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
