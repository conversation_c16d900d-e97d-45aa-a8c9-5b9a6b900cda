import { But<PERSON>, <PERSON>, Modal, Popconfirm, Tabs } from "antd";
import { useCallback, useEffect, useState } from "react";
import platformAccountConfig from "@/config/platformAccount";
import type { DouyinAccountsData } from "@/lib/douyinLogin";
import type { KuaishouAccountsData } from "@/lib/kuaishouLogin";
import useAccountStore from "@/store/accountStore";
import Account from "./account";

export default function AccountPage() {
  const { setAccountInfo, isLoginDialogOpen, setIsLoginDialogOpen } = useAccountStore();

  const [douyinAccounts, setDouyinAccounts] = useState<DouyinAccountsData | null>(null);
  const [kuaishouAccounts, setKuaishouAccounts] = useState<KuaishouAccountsData | null>(null);

  // 检查登录状态
  const checkLoginStatus = useCallback(async () => {
    try {
      const hasLoginInfo = await window.electronAPI.douyin.hasLoginInfo();
      let loginInfo = null;

      if (hasLoginInfo) {
        loginInfo = await window.electronAPI.douyin.getLoginInfo();
      }

      // setLoginStatus({ hasLoginInfo, loginInfo });
      console.log("loginInfo", loginInfo);
    } catch (error) {
      console.error("检查登录状态失败:", error);
    }
  }, []);

  // 加载抖音账号列表
  const loadDouyinAccounts = useCallback(async () => {
    try {
      const accountsData = await window.electronAPI.douyin.getAllAccountsData();
      setDouyinAccounts(accountsData);
    } catch (error) {
      console.error("加载抖音账号列表失败:", error);
    }
  }, []);

  // 加载快手账号列表
  const loadKuaishouAccounts = useCallback(async () => {
    try {
      const accountsData = await window.electronAPI.kuaishou.getAllAccountsData();
      setKuaishouAccounts(accountsData);
    } catch (error) {
      console.error("加载快手账号列表失败:", error);
    }
  }, []);

  // 删除抖音账号
  const deleteDouyinAccount = async (accountId: string) => {
    try {
      const result = await window.electronAPI.douyin.deleteAccountLoginInfo(accountId);
      if (result.success) {
        await loadDouyinAccounts(); // 重新加载账号列表
      } else {
        alert("删除账号失败");
      }
    } catch (error) {
      console.error("删除抖音账号失败:", error);
      alert("删除账号失败");
    }
  };

  // 删除快手账号
  const deleteKuaishouAccount = async (accountId: string) => {
    try {
      const result = await window.electronAPI.kuaishou.deleteAccountLoginInfo(accountId);
      if (result.success) {
        await loadKuaishouAccounts(); // 重新加载账号列表
      } else {
        alert("删除账号失败");
      }
    } catch (error) {
      console.error("删除快手账号失败:", error);
      alert("删除账号失败");
    }
  };

  // 打开抖音账号
  const openDouyinAccount = async (accountId: string) => {
    try {
      const result = await window.electronAPI.douyin.openAccount(accountId);
      if (result.success) {
        console.log("抖音账号浏览器窗口已打开");
      } else {
        alert(`打开账号失败: ${result.message}`);
      }
    } catch (error) {
      console.error("打开抖音账号失败:", error);
      alert("打开账号失败，请重试");
    }
  };

  // 打开快手账号
  const openKuaishouAccount = async (accountId: string) => {
    try {
      const result = await window.electronAPI.kuaishou.openAccount(accountId);
      if (result.success) {
        console.log("快手账号浏览器窗口已打开");
      } else {
        alert(`打开账号失败: ${result.message}`);
      }
    } catch (error) {
      console.error("打开快手账号失败:", error);
      alert("打开账号失败，请重试");
    }
  };

  const handleDouyinLogin = async (accountId?: string) => {
    try {
      const result = await window.electronAPI.douyin.startLogin(accountId);

      console.log("抖音登录窗口创建结果: ", result);

      if (result.success) {
        // 登录窗口创建成功，等待用户完成登录操作
        // 用户需要在登录窗口中完成登录并点击"保存状态"按钮
        console.log("登录窗口已创建，请完成登录操作");
        setIsLoginDialogOpen(false);
      } else {
        alert(`创建登录窗口失败: ${result.message}`);
      }
    } catch (error) {
      console.error("抖音登录失败:", error);
      alert("登录失败，请重试");
    }
  };

  const handleKuaishouLogin = async (accountId?: string) => {
    try {
      const result = await window.electronAPI.kuaishou.startLogin(accountId);

      console.log("快手登录窗口创建结果: ", result);

      if (result.success) {
        // 登录窗口创建成功，等待用户完成登录操作
        // 用户需要在登录窗口中完成登录并点击"保存状态"按钮
        console.log("登录窗口已创建，请完成登录操作");
        setIsLoginDialogOpen(false);
      } else {
        alert(`创建登录窗口失败: ${result.message}`);
      }
    } catch (error) {
      console.error("快手登录失败:", error);
      alert("登录失败，请重试");
    }
  };

  const handleXiaohongshuLogin = async () => {
    try {
      const result = await window.electronAPI.xiaohongshu.startLogin();

      if (result.success) {
        // 登录成功，重新检查状态
        await checkLoginStatus();
        setIsLoginDialogOpen(false);
      } else {
        alert(`登录失败: ${result.message}`);
      }
    } catch (error) {
      console.error("小红书登录失败:", error);
      alert("登录失败，请重试");
    } finally {
    }
  };

  const handleShipinhaoLogin = async () => {
    try {
      console.log("开始视频号登录");
      const result = await window.electronAPI.shipinhao.startLogin();

      console.log("视频号登录窗口创建结果: ", result);

      if (result.success) {
        // 登录窗口创建成功，等待用户完成登录操作
        // 用户需要在登录窗口中完成登录并点击"保存状态"按钮
        console.log("登录窗口已创建，请完成登录操作");
        setIsLoginDialogOpen(false);
      } else {
        alert(`创建登录窗口失败: ${result.message}`);
      }
    } catch (error) {
      console.error("视频号登录失败:", error);
      alert("登录失败，请重试");
    }
  };

  const handleBilibiliLogin = async () => {
    try {
      const result = await window.electronAPI.bilibili.startLogin();

      console.log("哔哩哔哩登录窗口创建结果: ", result);

      if (result.success) {
        // 登录窗口创建成功，等待用户完成登录操作
        // 用户需要在登录窗口中完成登录并点击"保存状态"按钮
        console.log("登录窗口已创建，请完成登录操作");
        setIsLoginDialogOpen(false);
      } else {
        alert(`创建登录窗口失败: ${result.message}`);
      }
    } catch (error) {
      console.error("哔哩哔哩登录失败:", error);
      alert("登录失败，请重试");
    }
  };

  const handlePlatformLogin = (platform: string) => {
    console.log("handlePlatformLogin", platform);
    switch (platform) {
      case "DouYinWeb":
        handleDouyinLogin(); // 不传accountId，表示添加新账号
        break;
      case "KuaiShouWeb":
        handleKuaishouLogin(); // 不传accountId，表示添加新账号
        break;
      case "XiaoHongShuWebCreator":
        handleXiaohongshuLogin();
        break;
      case "ShiPinHaoWeb":
        handleShipinhaoLogin();
        break;
      case "BiliBiliWeb":
        handleBilibiliLogin();
        break;
      default:
    }
  };
  const getUserBind = async () => {
    try {
      const { code, records } = await window.electronAPI.getUserBind();
      if (code === 0) {
        setAccountInfo(records);
      }
    } catch (error) {
      console.error("获取用户绑定信息失败:", error);
      // 如果是 401 错误，说明 token 无效，清除账户状态
      if (error && typeof error === "object" && "response" in error) {
        const axiosError = error as any;
        if (axiosError.response?.status === 401) {
          console.log("Token 无效，清除账户状态");
          setAccountInfo([]);
        }
      }
    }
  };

  useEffect(() => {
    getUserBind();
  }, [setAccountInfo]);

  // 加载抖音账号列表
  useEffect(() => {
    loadDouyinAccounts();
  }, [loadDouyinAccounts]);

  // 加载快手账号列表
  useEffect(() => {
    loadKuaishouAccounts();
  }, [loadKuaishouAccounts]);

  // 监听抖音登录完成事件
  useEffect(() => {
    const handleLoginCompleted = (data: any) => {
      // 刷新列表
      getUserBind();

      console.log("收到抖音登录完成事件:", data);
      if (data.success) {
        // 登录成功，刷新账号列表
        loadDouyinAccounts();
        console.log("抖音登录成功，已刷新账号列表");
      } else {
        // 登录失败，显示错误信息
        alert(`登录失败: ${data.error || "未知错误"}`);
      }
    };

    // 注册登录完成事件监听器
    window.electronAPI.douyin.onLoginCompleted(handleLoginCompleted);

    // 清理函数
    return () => {
      // 注意：这里可能需要手动移除监听器，但ipcRenderer.on没有直接的移除方法
      // 在实际使用中，这个组件卸载时会自动清理
    };
  }, [loadDouyinAccounts]);

  // 监听快手登录完成事件
  useEffect(() => {
    const handleLoginCompleted = (data: any) => {
      // 刷新列表
      getUserBind();

      console.log("收到快手登录完成事件:", data);
      if (data.success) {
        // 登录成功，刷新账号列表
        loadKuaishouAccounts();
        console.log("快手登录成功，已刷新账号列表");
      } else {
        // 登录失败，显示错误信息
        alert(`登录失败: ${data.error || "未知错误"}`);
      }
    };

    // 注册登录完成事件监听器
    window.electronAPI.kuaishou.onLoginCompleted(handleLoginCompleted);

    // 清理函数
    return () => {
      // 注意：这里可能需要手动移除监听器，但ipcRenderer.on没有直接的移除方法
      // 在实际使用中，这个组件卸载时会自动清理
    };
  }, [loadKuaishouAccounts]);

  // 监听视频号登录完成事件
  useEffect(() => {
    const handleLoginCompleted = (data: any) => {
      console.log("收到视频号登录完成事件:", data);
    };

    // 注册登录完成事件监听器
    window.electronAPI.shipinhao.onLoginCompleted(handleLoginCompleted);

    // 清理函数
    return () => {
      // 注意：这里可能需要手动移除监听器，但ipcRenderer.on没有直接的移除方法
      // 在实际使用中，这个组件卸载时会自动清理
    };
  }, []);

  const tabItems = [
    {
      key: "accounts",
      label: "账号管理",
      children: (
        <div className="flex flex-col gap-4">
          <Account />

          {/* 测试用户信息获取 */}
          <div className="mt-4 p-4 border rounded-lg bg-gray-50">
            <h4 className="text-md font-medium mb-2">用户信息获取测试</h4>
            <div className="flex gap-2">
              <Button
                onClick={async () => {
                  try {
                    const accountsData = await window.electronAPI.douyin.getAllAccountsData();
                    if (accountsData) {
                      console.log("所有抖音账号数据:", accountsData);
                      alert(`找到 ${Object.keys(accountsData.accounts).length} 个抖音账号\n详细信息请查看控制台`);
                    } else {
                      alert("没有找到抖音账号数据");
                    }
                  } catch (error) {
                    console.error("获取抖音账号数据失败:", error);
                    alert("获取抖音账号数据失败");
                  }
                }}
              >
                查看抖音账号信息
              </Button>
              <Button
                onClick={async () => {
                  try {
                    const accountsData = await window.electronAPI.kuaishou.getAllAccountsData();
                    if (accountsData) {
                      console.log("所有快手账号数据:", accountsData);
                      alert(`找到 ${Object.keys(accountsData.accounts).length} 个快手账号\n详细信息请查看控制台`);
                    } else {
                      alert("没有找到快手账号数据");
                    }
                  } catch (error) {
                    console.error("获取快手账号数据失败:", error);
                    alert("获取快手账号数据失败");
                  }
                }}
              >
                查看快手账号信息
              </Button>
            </div>
          </div>

          {/* 抖音账号列表 */}
          {douyinAccounts && Object.keys(douyinAccounts.accounts).length > 0 && (
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-3">抖音账号列表</h3>
              <List
                dataSource={Object.entries(douyinAccounts.accounts)}
                renderItem={([accountId, accountInfo]) => (
                  <List.Item
                    actions={[
                      <Button
                        key="bind"
                        type="link"
                        onClick={async () => {
                          try {
                            const result = await window.electronAPI.bindSingleAccount({
                              accountId,
                              platform: "DouYinWeb",
                              userId: "37",
                              enUserName: "wangkt",
                            });
                            if (result.success !== false) {
                              alert("账号绑定成功！");
                            } else {
                              alert(`绑定失败: ${result.message}`);
                            }
                          } catch (error) {
                            console.error("绑定账号失败:", error);
                            alert("绑定失败，请重试");
                          }
                        }}
                      >
                        绑定
                      </Button>,
                      <Button key="login" type="link" onClick={() => handleDouyinLogin(accountId)}>
                        重新登录
                      </Button>,
                      <Button key="open" type="link" onClick={() => openDouyinAccount(accountId)}>
                        打开
                      </Button>,
                      <Popconfirm key="delete" title="确定要删除这个账号吗？" onConfirm={() => deleteDouyinAccount(accountId)} okText="确定" cancelText="取消">
                        <Button type="link" danger>
                          删除
                        </Button>
                      </Popconfirm>,
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        accountInfo.userInfo?.avatar ? (
                          <img
                            src={accountInfo.userInfo.avatar}
                            alt="用户头像"
                            className="w-10 h-10 rounded-full object-cover"
                            onError={(e) => {
                              // 头像加载失败时显示默认头像
                              e.currentTarget.style.display = "none";
                              e.currentTarget.nextElementSibling?.classList.remove("hidden");
                            }}
                          />
                        ) : null
                      }
                      title={
                        <div className="flex items-center gap-2">
                          <span>{accountInfo.userInfo?.nickname || `抖音账号 ${accountId}`}</span>
                          {accountInfo.userInfo?.userId && <span className="text-xs text-gray-500">ID: {accountInfo.userInfo.userId}</span>}
                        </div>
                      }
                      description={
                        <div className="space-y-1">
                          <div>账号ID: {accountId}</div>
                          <div>登录时间: {new Date(accountInfo.timestamp).toLocaleString()}</div>
                          {!accountInfo.userInfo?.avatar && <div className="text-xs text-gray-400">未获取到用户头像</div>}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </div>
          )}

          {/* 快手账号列表 */}
          {kuaishouAccounts && Object.keys(kuaishouAccounts.accounts).length > 0 && (
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-3">快手账号列表</h3>
              <List
                dataSource={Object.entries(kuaishouAccounts.accounts)}
                renderItem={([accountId, accountInfo]) => (
                  <List.Item
                    actions={[
                      <Button
                        key="bind"
                        type="link"
                        onClick={async () => {
                          try {
                            const result = await window.electronAPI.kuaishou.createSingleAccountFileAndBind(accountId, "KuaiShouWeb", "37", "wangkt");
                            if (result.success) {
                              alert("账号绑定成功！");
                            } else {
                              alert(`绑定失败: ${result.message}`);
                            }
                          } catch (error) {
                            console.error("绑定账号失败:", error);
                            alert("绑定失败，请重试");
                          }
                        }}
                      >
                        绑定
                      </Button>,
                      <Button key="login" type="link" onClick={() => handleKuaishouLogin(accountId)}>
                        重新登录
                      </Button>,
                      <Button key="open" type="link" onClick={() => openKuaishouAccount(accountId)}>
                        打开
                      </Button>,
                      <Popconfirm key="delete" title="确定要删除这个账号吗？" onConfirm={() => deleteKuaishouAccount(accountId)} okText="确定" cancelText="取消">
                        <Button type="link" danger>
                          删除
                        </Button>
                      </Popconfirm>,
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        accountInfo.userInfo?.avatar ? (
                          <img
                            src={accountInfo.userInfo.avatar}
                            alt="用户头像"
                            className="w-10 h-10 rounded-full object-cover"
                            onError={(e) => {
                              // 头像加载失败时显示默认头像
                              e.currentTarget.style.display = "none";
                              e.currentTarget.nextElementSibling?.classList.remove("hidden");
                            }}
                          />
                        ) : null
                      }
                      title={
                        <div className="flex items-center gap-2">
                          <span>{accountInfo.userInfo?.nickname || `快手账号 ${accountId}`}</span>
                          {accountInfo.userInfo?.userId && <span className="text-xs text-gray-500">ID: {accountInfo.userInfo.userId}</span>}
                        </div>
                      }
                      description={
                        <div className="space-y-1">
                          <div>账号ID: {accountId}</div>
                          <div>登录时间: {new Date(accountInfo.timestamp).toLocaleString()}</div>
                          {!accountInfo.userInfo?.avatar && <div className="text-xs text-gray-400">未获取到用户头像</div>}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </div>
          )}
        </div>
      ),
    },
    {
      key: "groups",
      label: "分组管理",
      children: <div className="flex items-center justify-center h-[200px] text-muted-foreground">分组管理功能开发中</div>,
    },
    {
      key: "favorites",
      label: "收藏分组",
      children: <div className="flex items-center justify-center h-[200px] text-muted-foreground">收藏分组功能开发中</div>,
    },
  ];

  return (
    <div className="flex flex-col h-full p-3 sm:p-4 md:p-6 gap-4 md:gap-6">
      <Tabs defaultActiveKey="accounts" items={tabItems} />

      {/* 登录对话框 */}
      <Modal title="添加账号" open={isLoginDialogOpen} onCancel={() => setIsLoginDialogOpen(false)} width={400} footer={null}>
        <div className="my-8 grid grid-cols-3 justify-items-center gap-6">
          {Object.entries(platformAccountConfig).map(([key, value]) => {
            return (
              <div key={key} className="w-8 h-8 hover:cursor-pointer hover:scale-110 transition-all duration-300">
                <img
                  src={value.icon}
                  alt={value.name}
                  onClick={() => handlePlatformLogin(key)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handlePlatformLogin(key);
                    }
                  }}
                />
              </div>
            );
          })}
        </div>
      </Modal>
    </div>
  );
}
