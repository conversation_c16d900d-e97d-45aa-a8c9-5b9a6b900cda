import { ipcMain } from "electron";
import { XiaohongshuLoginService } from "../lib/xiaohongshuLogin";

export const setupXiaohongshuHandlers = (
  xiaohongshuLoginService: XiaohongshuLoginService | null,
  updateXiaohongshuLoginService: (
    service: XiaohongshuLoginService | null,
  ) => void,
) => {
  // 使用闭包来存储最新的登录服务实例
  let currentXiaohongshuLoginService = xiaohongshuLoginService;

  // 更新函数，同时更新闭包中的实例
  const updateService = (service: XiaohongshuLoginService | null) => {
    currentXiaohongshuLoginService = service;
    updateXiaohongshuLoginService(service);
  };

  // 开始小红书登录
  ipcMain.handle(
    "xiaohongshu:start-login",
    async (event, accountId?: string) => {
      console.log(
        "收到小红书登录请求...",
        accountId ? `账号ID: ${accountId}` : "新账号",
      );
      try {
        // 如果已有登录服务在运行，先关闭
        if (currentXiaohongshuLoginService) {
          console.log("关闭现有登录服务...");
          await currentXiaohongshuLoginService.close();
        }

        // 创建新的登录服务
        console.log("创建新的登录服务...");
        const newXiaohongshuLoginService = new XiaohongshuLoginService();
        await newXiaohongshuLoginService.startLogin(accountId);

        // 更新全局登录服务实例
        updateService(newXiaohongshuLoginService);

        return { success: true, message: "登录流程已启动" };
      } catch (error) {
        console.error("小红书登录失败:", error);
        return {
          success: false,
          message: error instanceof Error ? error.message : "登录失败",
        };
      }
    },
  );

  // 检查是否有登录信息
  ipcMain.handle("xiaohongshu:has-login-info", () => {
    console.log("检查是否有登录信息...");
    const hasInfo = XiaohongshuLoginService.hasLoginInfo();
    console.log("是否有登录信息:", hasInfo);
    return hasInfo;
  });

  // 检查是否有指定账号的登录信息
  ipcMain.handle(
    "xiaohongshu:has-account-login-info",
    (event, accountId: string) => {
      console.log(`检查账号 ${accountId} 是否有登录信息...`);
      const hasInfo = XiaohongshuLoginService.hasAccountLoginInfo(accountId);
      console.log(`账号 ${accountId} 是否有登录信息:`, hasInfo);
      return hasInfo;
    },
  );

  // 获取所有账号数据
  ipcMain.handle("xiaohongshu:get-all-accounts-data", () => {
    console.log("获取所有账号数据...");
    const accountsData = XiaohongshuLoginService.getAllAccountsData();
    console.log(
      "账号数据:",
      accountsData
        ? `包含 ${Object.keys(accountsData.accounts).length} 个账号`
        : "不存在",
    );
    return accountsData;
  });

  // 获取指定账号的登录信息
  ipcMain.handle(
    "xiaohongshu:get-account-login-info",
    (event, accountId: string) => {
      console.log(`获取账号 ${accountId} 的登录信息...`);
      const loginInfo = XiaohongshuLoginService.getAccountLoginInfo(accountId);
      console.log(
        `账号 ${accountId} 的登录信息:`,
        loginInfo ? "存在" : "不存在",
      );
      return loginInfo;
    },
  );

  // 获取登录信息（向后兼容）
  ipcMain.handle("xiaohongshu:get-login-info", () => {
    console.log("获取登录信息...");
    const loginInfo = XiaohongshuLoginService.getLoginInfo();
    console.log("登录信息:", loginInfo ? "存在" : "不存在");
    return loginInfo;
  });

  // 删除指定账号的登录信息
  ipcMain.handle(
    "xiaohongshu:delete-account-login-info",
    (event, accountId: string) => {
      console.log(`删除账号 ${accountId} 的登录信息...`);
      const success = XiaohongshuLoginService.deleteAccountLoginInfo(accountId);
      console.log(
        `删除账号 ${accountId} 的登录信息:`,
        success ? "成功" : "失败",
      );
      return success;
    },
  );

  // 打开指定账号
  ipcMain.handle(
    "xiaohongshu:open-account",
    async (event, accountId: string) => {
      console.log(`打开账号 ${accountId}...`);
      try {
        // 如果已有登录服务在运行，先关闭
        if (currentXiaohongshuLoginService) {
          console.log("关闭现有登录服务...");
          await currentXiaohongshuLoginService.close();
        }

        // 创建新的登录服务并打开账号
        console.log("创建新的登录服务...");
        const newXiaohongshuLoginService = new XiaohongshuLoginService();
        await newXiaohongshuLoginService.openAccount(accountId);

        // 更新全局登录服务实例
        updateService(newXiaohongshuLoginService);

        return { success: true, message: `账号 ${accountId} 已打开` };
      } catch (error) {
        console.error(`打开账号 ${accountId} 失败:`, error);
        return {
          success: false,
          message: error instanceof Error ? error.message : "打开账号失败",
        };
      }
    },
  );

  // 创建单个账号文件并绑定
  ipcMain.handle(
    "xiaohongshu:create-single-account-file-and-bind",
    async (
      event,
      accountId: string,
      platform: string,
      userId: string = "37",
      enUserName: string = "wangkt",
    ) => {
      console.log(`为账号 ${accountId} 创建绑定文件...`);
      try {
        const result =
          await XiaohongshuLoginService.createSingleAccountFileAndBind(
            accountId,
            platform,
            userId,
            enUserName,
          );
        return { success: true, data: result };
      } catch (error) {
        console.error(`为账号 ${accountId} 创建绑定文件失败:`, error);
        return {
          success: false,
          message: error instanceof Error ? error.message : "创建绑定文件失败",
        };
      }
    },
  );

  // 监听登录确认事件
  ipcMain.on("xiaohongshu:login-confirmed", async () => {
    console.log("收到登录确认信号...");
    console.log(
      "当前登录服务实例:",
      currentXiaohongshuLoginService ? "存在" : "不存在",
    );
    if (currentXiaohongshuLoginService) {
      await currentXiaohongshuLoginService.handleLoginConfirmation();
    } else {
      console.error("XiaohongshuLoginService 未初始化");
    }
  });
};
