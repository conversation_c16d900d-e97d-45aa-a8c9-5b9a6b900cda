import type { ShipinhaoLoginService } from "@/lib/shipinhaoLogin";
import type { BilibiliLoginService } from "../lib/bilibiliLogin";
import type { DouyinLoginService } from "../lib/douyinLogin";
import type { KuaishouLoginService } from "../lib/kuaishouLogin";
import type { XiaohongshuLoginService } from "../lib/xiaohongshuLogin";
import { setupAccountHandlers } from "./accountHandlers";
import { setupBilibiliHandlers } from "./bilibiliHandlers";
import { setupDouyinHandlers } from "./douyinHandlers";
import { setupKuaishouHandlers } from "./kuaishouHandlers";
import { setupLoginHandlers } from "./loginHandlers";
import { setupShipinhaoHandlers } from "./shipinhaoHandlers";
import { setupTaskHandlers } from "./taskHandlers";
import { registerTokenHandlers } from "./tokenHandlers";
import { setupXiaohongshuHandlers } from "./xiaohongshuHandlers";

export const setupAllHandlers = (
  douyinLoginService: DouyinLoginService | null,
  kuaishouLoginService: KuaishouLoginService | null,
  xiaohongshuLoginService: XiaohongshuLoginService | null,
  shipinhaoLoginService: ShipinhaoLoginService | null,
  bilibiliLoginService: BilibiliLoginService | null,
  updateDouyinLoginService: (service: DouyinLoginService | null) => void,
  updateKuaishouLoginService: (service: KuaishouLoginService | null) => void,
  updateXiaohongshuLoginService: (
    service: XiaohongshuLoginService | null,
  ) => void,
  updateShipinhaoLoginService: (service: ShipinhaoLoginService | null) => void,
  updateBilibiliLoginService: (service: BilibiliLoginService | null) => void,
) => {
  setupDouyinHandlers(douyinLoginService, updateDouyinLoginService);
  setupKuaishouHandlers(kuaishouLoginService, updateKuaishouLoginService);
  setupXiaohongshuHandlers(
    xiaohongshuLoginService,
    updateXiaohongshuLoginService,
  );
  setupShipinhaoHandlers(shipinhaoLoginService, updateShipinhaoLoginService);
  setupBilibiliHandlers(bilibiliLoginService, updateBilibiliLoginService);
  setupAccountHandlers();
  setupTaskHandlers();
  registerTokenHandlers();
  setupLoginHandlers();
};
