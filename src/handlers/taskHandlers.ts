import { ipc<PERSON>ain } from "electron";
import FormData from "form-data";
import instance from "../lib/axios";

export const setupTaskHandlers = () => {
  // 创建发布任务
  ipcMain.handle("create-publish-task", async (_, task: any) => {
    console.log("创建发布任务: ", task);
    // 重新组装为 FormData
    const formData = new FormData();
    if (task.video_files) {
      formData.append("video_files", Buffer.from(task.video_files), {
        filename: task.video_file_name,
      });
    }
    if (task.image_files) {
      formData.append("image_files", Buffer.from(task.image_files), {
        filename: task.image_file_name,
      });
    }
    if (task.bind_ids) formData.append("bind_ids", task.bind_ids);
    if (task.title) formData.append("title", task.title);
    if (task.topics) formData.append("topics", task.topics);
    if (task.description) formData.append("description", task.description);
    if (task.content_type) formData.append("content_type", task.content_type);
    if (task.user_id) formData.append("user_id", task.user_id);
    if (task.en_user_name) formData.append("en_user_name", task.en_user_name);

    const publishTask = await instance({
      url: "/svr/task/create_task",
      method: "POST",
      data: formData,
      headers: formData.getHeaders
        ? formData.getHeaders()
        : { "content-type": "multipart/form-data" },
    });
    console.log("publishTask: ", publishTask);
    return publishTask;
  });
};
