import { ipcMain } from "electron";
import { BilibiliLoginService } from "../lib/bilibiliLogin";

export const setupBilibiliHandlers = (
  bilibiliLoginService: BilibiliLoginService | null,
  updateBilibiliLoginService: (
    service: BilibiliLoginService | null,
  ) => void,
) => {
  // 使用闭包来存储最新的登录服务实例
  let currentBilibiliLoginService = bilibiliLoginService;

  // 更新函数，同时更新闭包中的实例
  const updateService = (service: BilibiliLoginService | null) => {
    currentBilibiliLoginService = service;
    updateBilibiliLoginService(service);
  };

  // 开始哔哩哔哩登录
  ipcMain.handle(
    "bilibili:start-login",
    async (event, accountId?: string) => {
      console.log(
        "收到哔哩哔哩登录请求...",
        accountId ? `账号ID: ${accountId}` : "新账号",
      );
      try {
        // 如果已有登录服务在运行，先关闭
        if (currentBilibiliLoginService) {
          console.log("关闭现有登录服务...");
          await currentBilibiliLoginService.close();
        }

        // 创建新的登录服务
        console.log("创建新的登录服务...");
        const newBilibiliLoginService = new BilibiliLoginService();
        await newBilibiliLoginService.startLogin(accountId);

        // 更新全局登录服务实例
        updateService(newBilibiliLoginService);

        return { success: true, message: "登录流程已启动" };
      } catch (error) {
        console.error("哔哩哔哩登录失败:", error);
        return {
          success: false,
          message: error instanceof Error ? error.message : "登录失败",
        };
      }
    },
  );

  // 检查是否有登录信息
  ipcMain.handle("bilibili:has-login-info", () => {
    console.log("检查是否有登录信息...");
    const hasInfo = BilibiliLoginService.hasLoginInfo();
    console.log("是否有登录信息:", hasInfo);
    return hasInfo;
  });

  // 检查是否有指定账号的登录信息
  ipcMain.handle(
    "bilibili:has-account-login-info",
    (event, accountId: string) => {
      console.log(`检查账号 ${accountId} 是否有登录信息...`);
      const hasInfo = BilibiliLoginService.hasAccountLoginInfo(accountId);
      console.log(`账号 ${accountId} 是否有登录信息:`, hasInfo);
      return hasInfo;
    },
  );

  // 获取所有账号数据
  ipcMain.handle("bilibili:get-all-accounts-data", () => {
    console.log("获取所有账号数据...");
    const accountsData = BilibiliLoginService.getAllAccountsData();
    console.log(
      "账号数据:",
      accountsData
        ? `包含 ${Object.keys(accountsData.accounts).length} 个账号`
        : "不存在",
    );
    return accountsData;
  });

  // 获取指定账号的登录信息
  ipcMain.handle(
    "bilibili:get-account-login-info",
    (event, accountId: string) => {
      console.log(`获取账号 ${accountId} 的登录信息...`);
      const loginInfo = BilibiliLoginService.getAccountLoginInfo(accountId);
      console.log(
        `账号 ${accountId} 的登录信息:`,
        loginInfo ? "存在" : "不存在",
      );
      return loginInfo;
    },
  );

  // 获取登录信息（向后兼容）
  ipcMain.handle("bilibili:get-login-info", () => {
    console.log("获取登录信息...");
    const loginInfo = BilibiliLoginService.getLoginInfo();
    console.log("登录信息:", loginInfo ? "存在" : "不存在");
    return loginInfo;
  });

  // 删除指定账号的登录信息
  ipcMain.handle(
    "bilibili:delete-account-login-info",
    (event, accountId: string) => {
      console.log(`删除账号 ${accountId} 的登录信息...`);
      const success = BilibiliLoginService.deleteAccountLoginInfo(accountId);
      console.log(
        `删除账号 ${accountId} 的登录信息:`,
        success ? "成功" : "失败",
      );
      return success;
    },
  );

  // 打开指定账号
  ipcMain.handle(
    "bilibili:open-account",
    async (event, accountId: string) => {
      console.log(`打开账号 ${accountId}...`);
      try {
        // 如果已有登录服务在运行，先关闭
        if (currentBilibiliLoginService) {
          console.log("关闭现有登录服务...");
          await currentBilibiliLoginService.close();
        }

        // 创建新的登录服务并打开账号
        console.log("创建新的登录服务...");
        const newBilibiliLoginService = new BilibiliLoginService();
        await newBilibiliLoginService.openAccount(accountId);

        // 更新全局登录服务实例
        updateService(newBilibiliLoginService);

        return { success: true, message: `账号 ${accountId} 已打开` };
      } catch (error) {
        console.error(`打开账号 ${accountId} 失败:`, error);
        return {
          success: false,
          message: error instanceof Error ? error.message : "打开账号失败",
        };
      }
    },
  );

  // 创建单个账号文件并绑定
  ipcMain.handle(
    "bilibili:create-single-account-file-and-bind",
    async (
      event,
      accountId: string,
      platform: string,
      userId: string = "37",
      enUserName: string = "wangkt",
    ) => {
      console.log(`为账号 ${accountId} 创建绑定文件...`);
      try {
        const result =
          await BilibiliLoginService.createSingleAccountFileAndBind(
            accountId,
            platform,
            userId,
            enUserName,
          );
        return { success: true, data: result };
      } catch (error) {
        console.error(`为账号 ${accountId} 创建绑定文件失败:`, error);
        return {
          success: false,
          message: error instanceof Error ? error.message : "创建绑定文件失败",
        };
      }
    },
  );

  // 监听登录确认事件
  ipcMain.on("bilibili:login-confirmed", async () => {
    console.log("收到登录确认信号...");
    console.log(
      "当前登录服务实例:",
      currentBilibiliLoginService ? "存在" : "不存在",
    );
    if (currentBilibiliLoginService) {
      await currentBilibiliLoginService.handleLoginConfirmation();
    } else {
      console.error("BilibiliLoginService 未初始化");
    }
  });
};
