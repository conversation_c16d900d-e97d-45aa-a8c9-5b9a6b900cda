import { ipcMain } from "electron";
import { ShipinhaoLoginService } from "../lib/shipinhaoLogin";

export const setupShipinhaoHandlers = (
  shipinhaoLoginService: ShipinhaoLoginService | null,
  updateShipinhaoLoginService: (service: ShipinhaoLoginService | null) => void,
) => {
  // 使用闭包来存储最新的登录服务实例
  let currentShipinhaoLoginService = shipinhaoLoginService;

  // 更新函数，同时更新闭包中的实例
  const updateService = (service: ShipinhaoLoginService | null) => {
    currentShipinhaoLoginService = service;
    updateShipinhaoLoginService(service);
  };

  // 开始抖音登录
  ipcMain.handle("shipinhao:start-login", async (_, accountId?: string) => {
    console.log(
      "收到抖音登录请求...",
      accountId ? `账号ID: ${accountId}` : "新账号",
    );
    try {
      // 如果已有登录服务在运行，先关闭
      if (currentShipinhaoLoginService) {
        console.log("关闭现有登录服务...");
        await currentShipinhaoLoginService.close();
      }

      // 创建新的登录服务
      console.log("创建新的登录服务...");
      const newShipinhaoLoginService = new ShipinhaoLoginService();
      await newShipinhaoLoginService.startLogin(accountId);

      // 更新全局登录服务实例
      updateService(newShipinhaoLoginService);

      return { success: true, message: "登录流程已启动" };
    } catch (error) {
      console.error("抖音登录失败:", error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "登录失败",
      };
    }
  });

  // 检查是否有登录信息
  ipcMain.handle("shipinhao:has-login-info", () => {
    console.log("检查是否有登录信息...");
    const hasInfo = ShipinhaoLoginService.hasLoginInfo();
    console.log("是否有登录信息:", hasInfo);
    return hasInfo;
  });

  // 检查是否有指定账号的登录信息
  ipcMain.handle(
    "shipinhao:has-account-login-info",
    async (_, accountId: string) => {
      console.log("检查是否有指定账号的登录信息...", accountId);
      const hasInfo = ShipinhaoLoginService.hasAccountLoginInfo(accountId);
      console.log("是否有账号登录信息:", hasInfo);
      return hasInfo;
    },
  );

  // 获取所有账号的登录信息
  ipcMain.handle("shipinhao:get-all-accounts-data", () => {
    console.log("获取所有账号的登录信息...");
    const accountsData = ShipinhaoLoginService.getAllAccountsData();
    console.log("账号数据:", accountsData ? "存在" : "不存在");
    return accountsData;
  });

  // 获取指定账号的登录信息
  ipcMain.handle(
    "shipinhao:get-account-login-info",
    async (_, accountId: string) => {
      console.log("获取指定账号的登录信息...", accountId);
      const loginInfo = ShipinhaoLoginService.getAccountLoginInfo(accountId);
      console.log("账号登录信息:", loginInfo ? "存在" : "不存在");
      return loginInfo;
    },
  );

  // 获取登录信息（向后兼容）
  ipcMain.handle("shipinhao:get-login-info", () => {
    console.log("获取登录信息...");
    const loginInfo = ShipinhaoLoginService.getLoginInfo();
    console.log("登录信息:", loginInfo ? "存在" : "不存在");
    return loginInfo;
  });

  // 删除指定账号的登录信息
  ipcMain.handle(
    "shipinhao:delete-account-login-info",
    async (_, accountId: string) => {
      console.log("删除指定账号的登录信息...", accountId);
      const success = ShipinhaoLoginService.deleteAccountLoginInfo(accountId);
      console.log("删除结果:", success);
      return { success };
    },
  );

  // 监听登录确认事件
  ipcMain.on("shipinhao:login-confirmed", async (event) => {
    console.log("收到登录确认信号...");
    console.log(
      "当前登录服务实例:",
      currentShipinhaoLoginService ? "存在" : "不存在",
    );
    if (currentShipinhaoLoginService) {
      try {
        await currentShipinhaoLoginService.handleLoginConfirmation();
        // 登录完成，通知前端刷新账号列表
        event.sender.send("shipinhao:login-completed", { success: true });
      } catch (error) {
        console.error("登录确认处理失败:", error);
        event.sender.send("shipinhao:login-completed", {
          success: false,
          error: error instanceof Error ? error.message : "登录失败",
        });
      }
    } else {
      console.error("ShipinhaoLoginService 未初始化");
      event.sender.send("shipinhao:login-completed", {
        success: false,
        error: "登录服务未初始化",
      });
    }
  });

  // 获取登录文件路径
  ipcMain.handle("shipinhao:get-login-file-path", async () => {
    return ShipinhaoLoginService.getLoginFilePath();
  });

  // 打开指定账号的浏览器窗口
  ipcMain.handle("shipinhao:open-account", async (_, accountId: string) => {
    console.log("收到打开抖音账号请求...", accountId);
    try {
      // 如果已有登录服务在运行，先关闭
      if (currentShipinhaoLoginService) {
        console.log("关闭现有登录服务...");
        await currentShipinhaoLoginService.close();
      }

      // 创建新的登录服务
      console.log("创建新的登录服务...");
      const newShipinhaoLoginService = new ShipinhaoLoginService();
      await newShipinhaoLoginService.openAccount(accountId);

      // 更新全局登录服务实例
      updateService(newShipinhaoLoginService);

      return { success: true, message: "账号浏览器窗口已打开" };
    } catch (error) {
      console.error("打开视频号账号失败:", error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "打开账号失败",
      };
    }
  });
};
