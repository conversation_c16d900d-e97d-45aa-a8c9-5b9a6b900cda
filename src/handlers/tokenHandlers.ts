import { ipcMain } from "electron";
import { readToken, saveToken } from "../lib/tokenManager";

/**
 * 注册Token相关的IPC事件处理程序
 */
export function registerTokenHandlers() {
  // 处理设置（保存）token的请求
  ipcMain.handle("token:set", async (event, token: string, rememberMe: boolean) => {
    if (typeof token !== "string") {
      return { success: false, error: "Invalid token provided." };
    }
    try {
      await saveToken(token);
      return { success: true };
    } catch (error) {
      console.error("Failed to save token via IPC:", error);
      return { success: false, error: "Failed to save token." };
    }
  });

  // 处理读取token的请求
  ipcMain.handle("token:get", async () => {
    try {
      const token = await readToken();
      console.log("get token", token);
      return { success: true, token };
    } catch (error) {
      console.error("Failed to read token via IPC:", error);
      return { success: false, error: "Failed to read token." };
    }
  });
}
