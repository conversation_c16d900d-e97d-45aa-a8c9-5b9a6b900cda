import { ipcMain } from "electron";
import FormData from "form-data";
import { createReadStream } from "fs";
import instance from "../lib/axios";
import { DouyinLoginService } from "../lib/douyinLogin";

export const setupAccountHandlers = () => {
  // 获取用户绑定平台列表
  ipcMain.handle("get-user-bind", async () => {
    const userBind = await instance(
      {
        url: "/svr/bind/get_user_bind?user_id=37",
        method: "GET",
      }
    );
    return userBind;
  });

  // 绑定用户
  ipcMain.handle("bind-user", async (_, data: any) => {
    console.log("绑定用户数据:", data);
    const formData = new FormData();
    formData.append("file", createReadStream(data.file));
    formData.append("user_id", data.user_id);
    formData.append("en_user_name", data.en_user_name);
    formData.append("platform", data.platform);
    formData.append("uuid", data.uuid);

    const userBind = await instance({
      url: "/svr/bind/bind_user",
      method: "POST",
      data: formData,
      headers: formData.getHeaders
        ? formData.getHeaders()
        : { "content-type": "multipart/form-data" },
    });
    console.log("绑定用户结果:", userBind);
    return userBind;
  });

  // 绑定单个账号
  ipcMain.handle("bind-single-account", async (_, data: any) => {
    console.log("绑定单个账号数据:", data);
    try {
      const {
        accountId,
        platform,
        userId = "37",
        enUserName = "wangkt",
      } = data;

      if (platform === "DouYinWeb") {
        const result = await DouyinLoginService.createSingleAccountFileAndBind(
          accountId,
          platform,
          userId,
          enUserName,
        );
        return result;
      } else {
        throw new Error(`不支持的平台: ${platform}`);
      }
    } catch (error) {
      console.error("绑定单个账号失败:", error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "绑定失败",
      };
    }
  });

  // 获取用户任务列表
  ipcMain.handle("get-user-tasks", async (_, userId: string) => {
    const userTasks = await instance.get(
      `/svr/task/get_user_tasks?user_id=${userId}`,
    );
    if (userTasks.code === 0) {
      return userTasks.records;
    } else {
      alert("获取用户任务列表失败");
      return [];
    }
  });
};
