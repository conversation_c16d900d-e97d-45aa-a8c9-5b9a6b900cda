import { ipcMain } from "electron";
import { DouyinLoginService } from "../lib/douyinLogin";

export const setupDouyinHandlers = (
  douyinLoginService: DouyinLoginService | null,
  updateDouyinLoginService: (service: DouyinLoginService | null) => void,
) => {
  // 使用闭包来存储最新的登录服务实例
  let currentDouyinLoginService = douyinLoginService;

  // 更新函数，同时更新闭包中的实例
  const updateService = (service: DouyinLoginService | null) => {
    currentDouyinLoginService = service;
    updateDouyinLoginService(service);
  };

  // 开始抖音登录
  ipcMain.handle("douyin:start-login", async (_, accountId?: string) => {
    console.log(
      "收到抖音登录请求...",
      accountId ? `账号ID: ${accountId}` : "新账号",
    );
    try {
      // 如果已有登录服务在运行，先关闭
      if (currentDouyinLoginService) {
        console.log("关闭现有登录服务...");
        await currentDouyinLoginService.close();
      }

      // 创建新的登录服务
      console.log("创建新的登录服务...");
      const newDouyinLoginService = new DouyinLoginService();
      await newDouyinLoginService.startLogin(accountId);

      // 更新全局登录服务实例
      updateService(newDouyinLoginService);

      return { success: true, message: "登录流程已启动" };
    } catch (error) {
      console.error("抖音登录失败:", error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "登录失败",
      };
    }
  });

  // 检查是否有登录信息
  ipcMain.handle("douyin:has-login-info", () => {
    console.log("检查是否有登录信息...");
    const hasInfo = DouyinLoginService.hasLoginInfo();
    console.log("是否有登录信息:", hasInfo);
    return hasInfo;
  });

  // 检查是否有指定账号的登录信息
  ipcMain.handle(
    "douyin:has-account-login-info",
    async (_, accountId: string) => {
      console.log("检查是否有指定账号的登录信息...", accountId);
      const hasInfo = DouyinLoginService.hasAccountLoginInfo(accountId);
      console.log("是否有账号登录信息:", hasInfo);
      return hasInfo;
    },
  );

  // 获取所有账号的登录信息
  ipcMain.handle("douyin:get-all-accounts-data", () => {
    console.log("获取所有账号的登录信息...");
    const accountsData = DouyinLoginService.getAllAccountsData();
    console.log("账号数据:", accountsData ? "存在" : "不存在");
    return accountsData;
  });

  // 获取指定账号的登录信息
  ipcMain.handle(
    "douyin:get-account-login-info",
    async (_, accountId: string) => {
      console.log("获取指定账号的登录信息...", accountId);
      const loginInfo = DouyinLoginService.getAccountLoginInfo(accountId);
      console.log("账号登录信息:", loginInfo ? "存在" : "不存在");
      return loginInfo;
    },
  );

  // 获取登录信息（向后兼容）
  ipcMain.handle("douyin:get-login-info", () => {
    console.log("获取登录信息...");
    const loginInfo = DouyinLoginService.getLoginInfo();
    console.log("登录信息:", loginInfo ? "存在" : "不存在");
    return loginInfo;
  });

  // 删除指定账号的登录信息
  ipcMain.handle(
    "douyin:delete-account-login-info",
    async (_, accountId: string) => {
      console.log("删除指定账号的登录信息...", accountId);
      const success = DouyinLoginService.deleteAccountLoginInfo(accountId);
      console.log("删除结果:", success);
      return { success };
    },
  );

  // 监听登录确认事件
  ipcMain.on("douyin:login-confirmed", async (event, userInfo: { en_user_name: string; user_id: string }) => {
    console.log("收到登录确认信号...");
    console.log(
      "当前登录服务实例:",
      currentDouyinLoginService ? "存在" : "不存在",
    );
    if (currentDouyinLoginService) {
      try {
        await currentDouyinLoginService.handleLoginConfirmation(userInfo);
        // 登录完成，通知前端刷新账号列表
        event.sender.send("douyin:login-completed", { success: true });
      } catch (error) {
        console.error("登录确认处理失败:", error);
        event.sender.send("douyin:login-completed", {
          success: false,
          error: error instanceof Error ? error.message : "登录失败",
        });
      }
    } else {
      console.error("DouyinLoginService 未初始化");
      event.sender.send("douyin:login-completed", {
        success: false,
        error: "登录服务未初始化",
      });
    }
  });

  // 获取登录文件路径
  ipcMain.handle("douyin:get-login-file-path", async () => {
    return DouyinLoginService.getLoginFilePath();
  });

  // 打开指定账号的浏览器窗口
  ipcMain.handle("douyin:open-account", async (_, accountId: string) => {
    console.log("收到打开抖音账号请求...", accountId);
    try {
      // 如果已有登录服务在运行，先关闭
      if (currentDouyinLoginService) {
        console.log("关闭现有登录服务...");
        await currentDouyinLoginService.close();
      }

      // 创建新的登录服务
      console.log("创建新的登录服务...");
      const newDouyinLoginService = new DouyinLoginService();
      await newDouyinLoginService.openAccount(accountId);

      // 更新全局登录服务实例
      updateService(newDouyinLoginService);

      return { success: true, message: "账号浏览器窗口已打开" };
    } catch (error) {
      console.error("打开抖音账号失败:", error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "打开账号失败",
      };
    }
  });
};
