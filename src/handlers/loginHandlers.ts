import { ipcMain } from "electron";
import instance from "../lib/axios";
import { clearToken, saveToken } from "../lib/tokenManager";

export const setupLoginHandlers = () => {
  ipcMain.handle("login", async (_, data: { en_user_name: string; p: string }) => {
    const loginResult = await instance.post("/svr/user/l", data);
    if (loginResult.code === 0) {
      await saveToken(loginResult.records.session_str);
      return { success: true, data: loginResult.records.user_info };
    }
    return { success: false };
  });
  ipcMain.handle("logout", async () => {
    await clearToken();
    return true;
  });
};
