import path from "node:path";
import { app, BrowserWindow } from "electron";
import started from "electron-squirrel-startup";
import { setupAllHandlers } from "./handlers";
import type { BilibiliLoginService } from "./lib/bilibiliLogin";
import type { DouyinLoginService } from "./lib/douyinLogin";
import type { KuaishouLoginService } from "./lib/kuaishouLogin";
import type { ShipinhaoLoginService } from "./lib/shipinhaoLogin";
import type { XiaohongshuLoginService } from "./lib/xiaohongshuLogin";

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (started) {
  app.quit();
}

// 全局变量存储登录服务实例
let douyinLoginService: DouyinLoginService | null = null;
let kuaishouLoginService: KuaishouLoginService | null = null;
let xiaohongshuLoginService: XiaohongshuLoginService | null = null;
let shipinhaoLoginService: ShipinhaoLoginService | null = null;
let bilibiliLoginService: BilibiliLoginService | null = null;

// 更新登录服务实例的函数
const updateDouyinLoginService = (service: DouyinLoginService | null) => {
  douyinLoginService = service;
};

const updateKuaishouLoginService = (service: KuaishouLoginService | null) => {
  kuaishouLoginService = service;
};

const updateXiaohongshuLoginService = (
  service: XiaohongshuLoginService | null,
) => {
  xiaohongshuLoginService = service;
};

const updateShipinhaoLoginService = (
  service: ShipinhaoLoginService | null,
) => {
  shipinhaoLoginService = service;
};
const updateBilibiliLoginService = (service: BilibiliLoginService | null) => {
  bilibiliLoginService = service;
};

const createWindow = () => {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, "preload.js"),
    },
  });

  // and load the index.html of the app.
  if (MAIN_WINDOW_VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(MAIN_WINDOW_VITE_DEV_SERVER_URL);
  } else {
    mainWindow.loadFile(
      path.join(__dirname, `../renderer/${MAIN_WINDOW_VITE_NAME}/index.html`),
    );
  }

  // Open the DevTools.
  mainWindow.webContents.openDevTools();
};

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on("ready", () => {
  setupAllHandlers(
    douyinLoginService,
    kuaishouLoginService,
    xiaohongshuLoginService,
    shipinhaoLoginService,
    bilibiliLoginService,
    updateDouyinLoginService,
    updateKuaishouLoginService,
    updateXiaohongshuLoginService,
    updateShipinhaoLoginService,
    updateBilibiliLoginService,
  );
  createWindow();
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});

app.on("activate", () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// 应用退出时清理资源
app.on("before-quit", async () => {
  if (douyinLoginService) {
    await douyinLoginService.close();
  }
  if (kuaishouLoginService) {
    await kuaishouLoginService.close();
  }
  if (xiaohongshuLoginService) {
    await xiaohongshuLoginService.close();
  }
  if (shipinhaoLoginService) {
    await shipinhaoLoginService.close();
  }
});

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and import them here.
