import type React from "react";
import MenuComponent from "@/components/template/menu";

export default function BaseLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex h-screen w-screen overflow-hidden">
      <div className="bg-[#F1F2F3]">
        <MenuComponent />
      </div>
      <main className="h-screen w-full pb-20 p-2 overflow-y-auto">
        {children}
      </main>
    </div>
  );
}
