import type { ContentTypeEnum, PlatformEnum } from "./accountType";

type TaskBody = {
  video_files: File;
  image_files: File;
  user_id: number;
  en_user_name: string;
  bind_ids: string;
  title: string;
  topic_ids: string;
  description: string;
  content_type: ContentTypeEnum;
};

type TaskListItem = {
  id: number;
  bind_list: { platform: PlatformEnum }[];
  user_id: number;
  en_user_name: string;
  content_type: ContentTypeEnum;
  title: string;
  topics: string;
  description: string;
  exe_time: string;
  exe_status: number;
};

export type { TaskBody, TaskListItem };
