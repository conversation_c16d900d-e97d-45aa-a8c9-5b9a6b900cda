enum PlatformEnum {
  BiliBiliWeb = "BiliBiliWeb",
  DouYinWeb = "DouYinWeb",
  KuaiShouWeb = "KuaiShouWeb",
  ShiPinHaoWeb = "ShiPinHaoWeb",
  TouTiaoWeb = "TouTiaoWeb",
  WeiBoWeb = "WeiBoWeb",
  XiaoHongShuWebCreator = "XiaoHongShuWebCreator",
  XiaoHongShuWebWWW = "XiaoHongShuWebWWW",
}
enum BindStatusEnum {
  Failed = -1,
  Binding = 0,
  Success = 1,
}

enum ContentTypeEnum {
  Video = "VIDEO",
  Image = "PIC_TEXT",
}

type AccountInfo = {
  accounts: {
    id: number;
    user_id: number;
    en_user_name: string;
    platform: PlatformEnum;
    bind_status: BindStatusEnum;
    status_reason: string;
    create_time: string;
    update_time: string;
    uuid: string;
  }[];
  isLoginDialogOpen: boolean;
};
type AccountInfoActions = {
  setAccountInfo: (accountInfo: AccountInfo["accounts"]) => void;
  setIsLoginDialogOpen: (isLoginDialogOpen: boolean) => void;
  isAuthenticated: () => boolean;
};

type AccountStore = AccountInfo & AccountInfoActions;

type BindUserInfo = {
  file: string;
  user_id: number;
  en_user_name: string;
  platform: PlatformEnum;
};

export { type AccountInfo, type AccountInfoActions, type AccountStore, type BindUserInfo, PlatformEnum, BindStatusEnum, ContentTypeEnum };
