import { Navigate, useLocation } from "@tanstack/react-router";
import type React from "react";
import { useEffect } from "react";
import { useLoginStore } from "@/store/loginStore";

interface AuthGuardProps {
  children: React.ReactNode;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const location = useLocation();
  const { setIsLogin, isLogin } = useLoginStore();

  // 检查是否有有效的token
  useEffect(() => {
    const checkToken = async () => {
      try {
        const result = await window.electronAPI.token.get();
        const hasToken = result.success && !!result.token;
        setIsLogin(hasToken);

        console.log("AuthGuard - token check result:", result);
        console.log("AuthGuard - isAuthenticated:", hasToken);
      } catch (error) {
        console.error("AuthGuard - token check failed:", error);
        setIsLogin(false);
      }
    };

    checkToken();
  }, []);

  // 如果还在检查token状态，显示加载状态
  if (isLogin === null) {
    return <div>Loading...</div>;
  }

  console.log("AuthGuard - location:", location.pathname);
  console.log("AuthGuard - isAuthenticated:", isLogin);

  // 如果未登录且不在登录页面，重定向到登录页面
  if (!isLogin && location.pathname !== "/login") {
    console.log("AuthGuard - 重定向到登录页面");
    return <Navigate to="/login" replace />;
  }

  // 如果已登录且在登录页面，重定向到账号页面
  if (isLogin && location.pathname === "/login") {
    console.log("AuthGuard - 重定向到账号页面");
    return <Navigate to="/" replace />;
  }

  console.log("AuthGuard - 允许访问:", location.pathname);
  return <>{children}</>;
};

export default AuthGuard;
