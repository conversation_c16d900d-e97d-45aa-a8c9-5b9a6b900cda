import { <PERSON>, useNavigate, useRouter<PERSON>tate } from "@tanstack/react-router";
import { Menu } from "antd";
import { BarChart3, FileVideo, Home, ImageUp, LogOut, MoreHorizontal, Scissors, Send, User } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { cn } from "@/lib/tailwind";
import { useLoginStore } from "@/store/loginStore";

const menuItems = [
  {
    label: "主页",
    icon: <Home size={16} strokeWidth={1.5} />,
    to: "/dashboard",
    key: "/dashboard",
  },
  {
    label: "发布",
    icon: <Send size={16} strokeWidth={1.5} />,
    to: "/publish",
    key: "/publish",
  },
  {
    label: "账号",
    icon: <User size={16} strokeWidth={1.5} />,
    to: "/",
    key: "/",
  },
  {
    label: "数据",
    icon: <BarChart3 size={16} strokeWidth={1.5} />,
    to: "/data",
    key: "/data",
  },
  {
    label: "混剪",
    icon: <Scissors size={16} strokeWidth={1.5} />,
    to: "/mix",
    key: "/mix",
    tag: "NEW",
  },
  {
    label: "更多",
    icon: <MoreHorizontal size={16} strokeWidth={1.5} />,
    to: "/more",
    key: "/more",
  },
  {
    label: "发布视频",
    icon: <FileVideo size={16} strokeWidth={1.5} />,
    to: "/publishVideo",
    key: "/publishVideo",
  },
  {
    label: "发布图片",
    icon: <ImageUp size={16} strokeWidth={1.5} />,
    to: "/publishImage",
    key: "/publishImage",
  },
];

export default function MenuComponent() {
  const { setIsLogin } = useLoginStore();
  const { location } = useRouterState();
  const navigate = useNavigate();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartX, setDragStartX] = useState(0);
  const menuRef = useRef<HTMLDivElement>(null);
  const { userInfo } = useLoginStore();

  const handleMouseEnter = useCallback(() => {
    setIsHovering(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovering(false);
  }, []);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStartX(e.clientX);
  }, []);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging) return;

      const deltaX = e.clientX - dragStartX;
      const threshold = 50; // 拖拽阈值

      if (Math.abs(deltaX) > threshold) {
        setIsCollapsed(deltaX < 0);
        setIsDragging(false);
      }
    },
    [isDragging, dragStartX],
  );

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  const handleMenuClick = ({ key }: { key: string }) => {
    const item = menuItems.find((item) => item.key === key);
    if (item) {
      navigate({ to: item.to });
    }
  };

  const handleLogout = async () => {
    try {
      console.log("开始退出登录...");
      // 清除账户状态
      setIsLogin(false);

      // 清空token信息
      await window.electronAPI.logout();
      console.log("账户状态已清除");
      // 最后导航到登录页面
      console.log("导航到登录页面");
      navigate({ to: "/login" });
    } catch (error) {
      console.error("退出登录失败:", error);
      // 即使出错也要清除本地状态并跳转
      setIsLogin(false);
      navigate({ to: "/login" });
    }
  };

  const antdMenuItems = [
    ...menuItems.map((item) => ({
      key: item.key,
      icon: item.icon,
      label: (
        <Link to={item.to} className="flex items-center gap-2">
          <span>{item.label}</span>
          {item.tag && !isCollapsed && <span className="text-xs bg-red-500 text-white rounded px-1 flex-shrink-0">NEW</span>}
        </Link>
      ),
    })),
    {
      type: "divider" as const,
    },
    {
      key: "logout",
      icon: <LogOut size={16} strokeWidth={1.5} />,
      label: (
        <button type="button" onClick={handleLogout} className="flex items-center gap-2 w-full text-left p-0 h-auto border-none shadow-none">
          <span>退出登录</span>
        </button>
      ),
    },
  ];

  return (
    <div ref={menuRef} className={cn("flex flex-col h-full bg-[#F6F7FB] shadow p-4 transition-all duration-300 ease-in-out relative", isCollapsed ? "w-20" : "w-44")}>
      {/* 拖拽区域 */}
      <button
        type="button"
        className="absolute top-0 right-0 w-1 h-full cursor-ew-resize z-10 border-none bg-transparent p-0"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onMouseDown={handleMouseDown}
        aria-label="调整菜单宽度"
      >
        <div className={cn("w-full h-full transition-opacity duration-200", isHovering || isDragging ? "bg-blue-500/20" : "bg-transparent")} />
      </button>

      {/* 头像区 */}
      <div className={cn("flex items-center gap-3 transition-all duration-300", isCollapsed ? "justify-center" : "")}>
        <div className="bg-[#FFE7BA] rounded-full p-2">
          <User size={20} strokeWidth={1.5} className="text-[#F7B500]" />
        </div>
        {!isCollapsed && (
          <div className="transition-opacity duration-300">
            <div className="font-bold">{userInfo?.user_nick_name}</div>
            <div className="text-xs text-[#A1A7B3] bg-[#F7B500] rounded px-1 py-0.5 inline-block">免费版</div>
          </div>
        )}
      </div>

      {/* 菜单项 */}
      <Menu
        mode="inline"
        selectedKeys={[location.pathname]}
        items={antdMenuItems}
        onClick={handleMenuClick}
        inlineCollapsed={isCollapsed}
        className="mt-4 border-none bg-transparent"
        style={{
          backgroundColor: "transparent",
          border: "none",
        }}
      />
    </div>
  );
}
