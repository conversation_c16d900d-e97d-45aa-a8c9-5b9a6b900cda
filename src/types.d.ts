import type { <PERSON>uy<PERSON>AccountsD<PERSON>, DouyinLoginInfo } from "./lib/douyinLogin";
import type { <PERSON><PERSON><PERSON>AccountsData, KuaishouLoginInfo } from "./lib/kuaishouLogin";
import type { XiaohongshuLoginInfo } from "./lib/xiaohongshuLogin";
import type { BindUserInfo } from "./types/accountType";
import type { TaskListItem } from "./types/task";
import type { UserInfo } from "./store/loginStore";

// declare const MAIN_WINDOW_VITE_DEV_SERVER_URL: string | undefined;
// declare const MAIN_WINDOW_VITE_NAME: string;

declare global {
  interface Window {
    electronAPI: {
      getUserBind: () => Promise<any>;
      createPublishTask: (task: any) => Promise<any>;
      bindUser: (data: any) => Promise<BindUserInfo>;
      bindSingleAccount: (data: any) => Promise<any>;
      getUserTasks: (userId: string) => Promise<TaskListItem[]>;
      login: (data: { en_user_name: string; p: string }) => Promise<{ success: boolean; data?: UserInfo }>;
      logout: () => Promise<boolean>;

      token: {
        get: () => Promise<{ success: boolean; token: string | undefined }>;
      };

      douyin: {
        startLogin: (accountId?: string) => Promise<{ success: boolean; message: string }>;
        hasLoginInfo: () => Promise<boolean>;
        hasAccountLoginInfo: (accountId: string) => Promise<boolean>;
        getAllAccountsData: () => Promise<DouyinAccountsData | null>;
        getAccountLoginInfo: (accountId: string) => Promise<DouyinLoginInfo | null>;
        getLoginInfo: () => Promise<DouyinLoginInfo | null>;
        deleteAccountLoginInfo: (accountId: string) => Promise<{ success: boolean }>;
        getLoginFilePath: () => string;
        openAccount: (accountId: string) => Promise<{ success: boolean; message: string }>;
        onLoginCompleted: (callback: (data: any) => void) => void;
      };
      kuaishou: {
        startLogin: (accountId?: string) => Promise<{ success: boolean; message: string }>;
        hasLoginInfo: () => Promise<boolean>;
        hasAccountLoginInfo: (accountId: string) => Promise<boolean>;
        getAllAccountsData: () => Promise<KuaishouAccountsData | null>;
        getAccountLoginInfo: (accountId: string) => Promise<KuaishouLoginInfo | null>;
        getLoginInfo: () => Promise<KuaishouLoginInfo | null>;
        deleteAccountLoginInfo: (accountId: string) => Promise<{ success: boolean }>;
        getLoginFilePath: () => string;
        openAccount: (accountId: string) => Promise<{ success: boolean; message: string }>;
        createSingleAccountFileAndBind: (accountId: string, platform: string, userId?: string, enUserName?: string) => Promise<{ success: boolean; data?: any; message?: string }>;
        onLoginCompleted: (callback: (data: any) => void) => void;
      };
      xiaohongshu: {
        startLogin: () => Promise<{ success: boolean; message: string }>;
        hasLoginInfo: () => Promise<boolean>;
        getLoginInfo: () => Promise<XiaohongshuLoginInfo | null>;
        onLoginCompleted: (callback: (data: any) => void) => void;
      };
      shipinhao: {
        startLogin: (accountId?: string) => Promise<{ success: boolean; message: string }>;
        hasLoginInfo: () => Promise<boolean>;
        hasAccountLoginInfo: (accountId: string) => Promise<boolean>;
        onLoginCompleted: (callback: (data: any) => void) => void;
      };
      bilibili: {
        startLogin: (accountId?: string) => Promise<{ success: boolean; message: string }>;
        hasLoginInfo: () => Promise<boolean>;
        onLoginCompleted: (callback: (data: any) => void) => void;
      };
    };
  }
}
