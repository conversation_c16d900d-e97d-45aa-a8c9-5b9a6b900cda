import { contextBridge, ipc<PERSON><PERSON><PERSON> } from "electron";

contextBridge.exposeInMainWorld("loginAPI", {
  confirmDouyinLogin: (userInfo: { en_user_name: string; user_id: string }) => ipcRenderer.send("douyin:login-confirmed", userInfo),
  confirmKuaishouLogin: () => ipcRenderer.send("kuaishou:login-confirmed"),
  confirmXiaohongshuLogin: () => ipcRenderer.send("xiaohongshu:login-confirmed"),
  confirmShipinhaoLogin: () => ipcRenderer.send("shipinhao:login-confirmed"),
  confirmBilibiliLogin: () => ipcRenderer.send("bilibili:login-confirmed"),
});
