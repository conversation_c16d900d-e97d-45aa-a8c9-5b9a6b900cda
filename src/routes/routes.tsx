import { createRoute } from "@tanstack/react-router";
import Account from "@/pages/account/index";
import Dashboard from "@/pages/dashboard";
import Data from "@/pages/data";
import Login from "@/pages/login";
import Mix from "@/pages/mix";
import More from "@/pages/more";
import Publish from "@/pages/publish";
import PublishImage from "@/pages/publishImage";
import PublishVideo from "@/pages/publishVideo";
import { RootRoute } from "./root";

export const LoginRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: "/login",
  component: Login,
});

export const DashboardRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: "/dashboard",
  component: Dashboard,
});

export const PublishRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: "/publish",
  component: Publish,
});

export const AccountRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: "/",
  component: Account,
});

export const DataRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: "/data",
  component: Data,
});

export const MixRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: "/mix",
  component: Mix,
});

export const MoreRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: "/more",
  component: More,
});

export const PublishVideoRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: "/publishVideo",
  component: PublishVideo,
});

export const PublishImageRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: "/publishImage",
  component: PublishImage,
});

export const rootTree = RootRoute.addChildren([LoginRoute, DashboardRoute, PublishRoute, AccountRoute, DataRoute, MixRoute, MoreRoute, PublishVideoRoute, PublishImageRoute]);
