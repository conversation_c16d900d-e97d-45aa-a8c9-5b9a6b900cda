import { createRootRoute, Outlet, useLocation } from "@tanstack/react-router";
import AuthGuard from "@/components/AuthGuard";
import BaseLayout from "@/layouts/baseLayout";

function Root() {
  const location = useLocation();
  const isLoginPage = location.pathname === "/login";

  return (
    <AuthGuard>
      {isLoginPage ? (
        <Outlet />
      ) : (
        <BaseLayout>
          <Outlet />
        </BaseLayout>
      )}
    </AuthGuard>
  );
}

export const RootRoute = createRootRoute({
  component: Root,
});
