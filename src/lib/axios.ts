import axios, { type AxiosInstance, type AxiosResponse, type InternalAxiosRequestConfig } from "axios";
import { readToken } from "./tokenManager";

// 创建axios实例
const instance: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_HOST,
  timeout: 10000,
  withCredentials: true,
  proxy: false,
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
instance.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    if (config.data instanceof FormData) {
      config.headers["Content-Type"] = "multipart/form-data";
      console.log("Request:", config.method?.toUpperCase(), config.url, config.headers);
    }

    // 异步获取token并添加到请求头
    try {
      const token = await readToken();
      if (token) {
        config.headers.Authorization = `${token}`;
      }
    } catch (error) {
      console.error("Failed to get token for request:", error);
      // 如果token不存在，则跳转登录
      window.location.href = "/login";
    }

    return config;
  },
  (error) => {
    console.error("Request Error:", error);
    return Promise.reject(error);
  },
);

// 响应拦截器
instance.interceptors.response.use(
  async (response: AxiosResponse) => {
    console.log("Response:", response.status, response.data);

    // 可以根据业务需求处理响应数据
    const { data, status } = response;

    // 如果响应成功但业务状态码不是200，可以在这里处理
    if (status === 200 && data.code !== 0) {
      // 处理业务错误
      console.warn("Business Error:", data.message || "Unknown error");

      // 如果是token过期，可以在这里处理
      if (data.code === 401) {
        // 可以在这里添加跳转逻辑
        window.location.href = "/login";
      }

      return Promise.reject(new Error(data.message || "Business error"));
    }

    return data;
  },
  async (error) => {
    console.error("Response Error:", error);

    // 处理网络错误
    if (error.response) {
      // 服务器返回了错误状态码
      const { status, data } = error.response;

      switch (status) {
        case 401:
          // 未授权，跳转登录
          window.location.href = "/login";
          break;
        case 403:
          console.error("Forbidden: Access denied");
          break;
        case 404:
          console.error("Not Found: Resource not found");
          break;
        case 500:
          console.error("Server Error: Internal server error");
          break;
        default:
          console.error(`HTTP Error: ${status}`, data);
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      console.error("Network Error: No response received");
    } else {
      // 请求配置出错
      console.error("Request Config Error:", error.message);
    }

    return Promise.reject(error);
  },
);

// 导出封装的axios实例
export default instance;
