import * as crypto from "node:crypto";
import * as fs from "node:fs/promises";
import * as path from "node:path";
import { app } from "electron";

// --- 安全警告 ---
// 为了演示，密钥和初始化向量(IV)被硬编码在此处。
// 在生产环境中，这会带来严重的安全风险。
// 您应该从一个安全的位置（例如，环境变量、macOS Keychain、Windows Credential Manager）加载它们。
const ALGORITHM = "aes-256-cbc";
const SECRET_KEY = process.env.TOKEN_SECRET_KEY || "a".repeat(32); // 必须是32字节
const IV = process.env.TOKEN_IV || "b".repeat(16); // 必须是16字节

const tokenFilePath = path.join(app.getPath("userData"), "token.json");

/**
 * 加密字符串
 * @param text 要加密的文本
 * @returns 加密后的十六进制字符串
 */
function encrypt(text: string): string {
  const cipher = crypto.createCipheriv(ALGORITHM, Buffer.from(SECRET_KEY), Buffer.from(IV));
  let encrypted = cipher.update(text, "utf8", "hex");
  encrypted += cipher.final("hex");
  return encrypted;
}

/**
 * 解密字符串
 * @param encryptedText 加密后的十六进制字符串
 * @returns 解密后的文本
 */
function decrypt(encryptedText: string): string {
  const decipher = crypto.createDecipheriv(ALGORITHM, Buffer.from(SECRET_KEY), Buffer.from(IV));
  let decrypted = decipher.update(encryptedText, "hex", "utf8");
  decrypted += decipher.final("utf8");
  return decrypted;
}

/**
 * 将加密后的token保存到文件
 * @param token 要保存的原始token字符串
 */
export async function saveToken(token: string): Promise<void> {
  try {
    const encryptedToken = encrypt(token);
    await fs.writeFile(tokenFilePath, JSON.stringify({ token: encryptedToken }));
  } catch (error) {
    console.error("Failed to save token:", error);
  }
}

/**
 * 从文件读取并解密token
 * @returns 解密后的token字符串，如果文件不存在或读取失败则返回null
 */
export async function readToken(): Promise<string | null> {
  try {
    const data = await fs.readFile(tokenFilePath, "utf-8");
    const { token: encryptedToken } = JSON.parse(data);
    if (encryptedToken) {
      return decrypt(encryptedToken);
    }
    return null;
  } catch (error) {
    // 如果文件不存在，这是正常情况（例如首次启动），直接返回null
    if (error instanceof Error && error.message.includes("ENOENT")) {
      return null;
    }
    console.error("Failed to read token:", error);
    return null;
  }
}

export async function clearToken() {
  // 删除对应文件
  const tokenFilePath = path.join(app.getPath("userData"), "token.json");
  await fs.unlink(tokenFilePath);
}
