import { createReadStream, existsSync, readFileSync } from "node:fs";
import { mkdir, unlink, writeFile } from "node:fs/promises";
import { join } from "node:path";
import { app, BrowserWindow } from "electron";
import log from "electron-log";
import FormData from "form-data";
import { PlatformEnum } from "../types/accountType";
import instance from "./axios";

const DATA_DIR = join(app.getPath("userData"), "data");
const LOGIN_INFO_PATH = join(DATA_DIR, "xiaohongshu.json");

export interface XiaohongshuLoginInfo {
  cookies: Array<{
    name: string;
    value: string;
    domain: string;
    path: string;
    expires: number;
    httpOnly: boolean;
    secure: boolean;
    sameSite: "Strict" | "Lax" | "None";
  }>;
  origins: Array<{
    origin: string;
    localStorage: Array<{
      name: string;
      value: string;
    }>;
  }>;
  sessionStorage: Record<string, string>;
  userAgent: string;
  timestamp: number;
  userInfo?: {
    nickname: string;
    avatar: string;
    userId: string;
  };
}

// 新的多账号数据结构
export interface XiaohongshuAccountsData {
  accounts: {
    [accountId: string]: XiaohongshuLoginInfo;
  };
  lastUpdated: number;
}

export class XiaohongshuLoginService {
  private loginWindow: BrowserWindow | null = null;
  private currentAccountId: string | null = null;

  async startLogin(accountId?: string): Promise<void> {
    // 如果没有提供accountId，生成一个新的
    this.currentAccountId = accountId || `xiaohongshu_${Date.now()}`;

    try {
      console.log("正在创建登录窗口...");

      // 创建新的浏览器窗口
      this.loginWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        title: "新授权",
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
          preload: join(__dirname, "login-preload.js"),
        },
        show: false, // 先不显示，等加载完成再显示
      });

      // 清空会话数据，确保每次都是全新的登录状态
      console.log("清空会话数据...");
      await this.loginWindow.webContents.session.clearStorageData({
        storages: [
          "cookies",
          "localstorage",
          "websql",
          "indexdb",
          "shadercache",
          "serviceworkers",
          "cachestorage",
        ],
      });
      console.log("会话数据已清空");

      // 设置用户代理
      this.loginWindow.webContents.setUserAgent(
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      );

      // 监听页面加载完成
      this.loginWindow.webContents.on("did-finish-load", () => {
        console.log("页面加载完成，准备显示窗口...");
        if (this.loginWindow && !this.loginWindow.isDestroyed()) {
          this.loginWindow.show();
          console.log("登录窗口已显示，准备注入暂停UI");

          // 延迟一下再注入UI，确保页面完全准备好
          setTimeout(() => {
            this.injectPauseUI();
          }, 1000);
        }
      });

      // 监听窗口关闭
      this.loginWindow.on("closed", () => {
        console.log("登录窗口被关闭");
        this.loginWindow = null;
      });

      // 监听窗口即将关闭
      this.loginWindow.on("close", (event) => {
        console.log("登录窗口即将关闭");
      });

      // 监听页面加载错误
      this.loginWindow.webContents.on(
        "did-fail-load",
        (event, errorCode, errorDescription) => {
          console.error("页面加载失败:", errorCode, errorDescription);
        },
      );

      // 导航到小红书创作者平台
      console.log("开始导航到小红书创作者平台...");
      await this.loginWindow.loadURL("https://creator.xiaohongshu.com/login");
      console.log("导航完成");
    } catch (error) {
      console.error("创建登录窗口时出错:", error);
      throw error;
    }
  }

  async handleLoginConfirmation(): Promise<void> {
    log.info("登录已确认，正在保存信息...");
    console.log("登录已确认，正在保存信息...");
    try {
      // 检查并创建 data 目录
      if (!existsSync(DATA_DIR)) {
        log.info("data 目录不存在，正在创建...");
        await mkdir(DATA_DIR, { recursive: true });
        log.info("data 目录创建成功");
      }

      await this.saveLoginInfo();

      console.log("开始提交登录信息...");

      // 获取当前账号的登录信息
      const currentAccountInfo = XiaohongshuLoginService.getAccountLoginInfo(
        this.currentAccountId!,
      );
      if (!currentAccountInfo) {
        throw new Error("无法获取当前账号的登录信息");
      }

      // 直接使用账号信息，不包含外层结构
      const tempLoginData = currentAccountInfo;

      // 将当前账号信息写入临时文件
      const tempFilePath = join(
        DATA_DIR,
        `xiaohongshu_temp_${this.currentAccountId}.json`,
      );

      await writeFile(tempFilePath, JSON.stringify(tempLoginData, null, 2));

      const fileData = createReadStream(tempFilePath);
      console.log("fileData: ", fileData);
      const formData = new FormData();
      formData.append("file", fileData);
      formData.append("user_id", "37");
      formData.append("en_user_name", "wangkt");
      formData.append("platform", PlatformEnum.XiaoHongShuWebCreator);
      formData.append("uuid", this.currentAccountId);

      log.info("开始提交登录信息...");
      const userBind = await instance({
        url: "/svr/bind/bind_user",
        method: "POST",
        data: formData,
        headers: formData.getHeaders
          ? formData.getHeaders()
          : { "content-type": "multipart/form-data" },
      });
      log.info("登录信息提交成功", userBind);

      console.log("登录信息提交成功", userBind);
      console.log("登录信息保存成功");

      // 删除临时文件
      try {
        await unlink(tempFilePath);
        console.log("临时文件已删除:", tempFilePath);
      } catch (error) {
        console.warn("删除临时文件失败:", error);
      }
    } catch (error) {
      console.error("保存登录信息时出错：", error);
      log.error("保存登录信息时出错：", error);
    } finally {
      if (this.loginWindow && !this.loginWindow.isDestroyed()) {
        this.loginWindow.close();
      }
    }
  }

  private async injectPauseUI(): Promise<void> {
    if (!this.loginWindow || this.loginWindow.isDestroyed()) {
      console.log("登录窗口不存在或已销毁，无法注入UI");
      return;
    }

    try {
      console.log("开始注入暂停UI...");

      // 先检查页面是否已经准备好
      const isPageReady = await this.loginWindow.webContents.executeJavaScript(`
        document.readyState === 'complete'
      `);

      if (!isPageReady) {
        console.log("页面未完全加载，等待...");
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }

      const result = await this.loginWindow.webContents.executeJavaScript(`
        (() => {
          try {
            console.log('开始创建暂停UI...');
            const oldBanner = document.getElementById('login-pause-banner');
            if (oldBanner) {
              oldBanner.remove();
              console.log('移除旧的banner');
            }

            const banner = document.createElement('div');
            banner.id = 'login-pause-banner';
            banner.style.cssText = \`
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              background-color: #fff3cd;
              padding: 12px 24px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              z-index: 99999;
              font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
              color: #856404;
              font-size: 14px;
              border-bottom: 1px solid #ffeaa7;
              box-sizing: border-box;
            \`;

            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = 'display: flex; align-items: center; gap: 8px;';

            const iconSvg = \`<svg viewBox="64 64 896 896" focusable="false" data-icon="pause-circle" width="1em" height="1em" fill="#856404" aria-hidden="true"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v224c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm224 0c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v224c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296z"></path></svg>\`;
            messageDiv.innerHTML = iconSvg;
            
            const textSpan = document.createElement('span');
            textSpan.textContent = '页面已暂停，请手动完成登录操作，然后点击"保存状态"按钮';
            messageDiv.appendChild(textSpan);

            const button = document.createElement('button');
            button.id = 'save-state-btn';
            button.textContent = '保存状态';
            button.style.cssText = \`
              background-color: #28a745;
              color: white;
              border: none;
              padding: 8px 16px;
              border-radius: 6px;
              font-size: 14px;
              cursor: pointer;
              font-weight: 500;
              transition: background-color 0.3s;
            \`;
            
            button.onmouseover = () => button.style.backgroundColor = '#218838';
            button.onmouseout = () => button.style.backgroundColor = '#28a745';

            button.onclick = () => {
              console.log('保存状态按钮被点击');
              try {
                if (window.loginAPI && window.loginAPI.confirmXiaohongshuLogin) {
                  console.log('调用 loginAPI.confirmXiaohongshuLogin()');
                  window.loginAPI.confirmXiaohongshuLogin();
                  button.textContent = '正在保存...';
                  button.disabled = true;
                  button.style.backgroundColor = '#6c757d';
                  button.style.cursor = 'not-allowed';
                } else {
                  console.error('loginAPI 不可用');
                  alert('登录API不可用，请刷新页面重试');
                }
              } catch (error) {
                console.error('按钮点击处理错误:', error);
                alert('保存失败: ' + error.message);
              }
            };

            banner.appendChild(messageDiv);
            banner.appendChild(button);
            document.body.appendChild(banner);
            console.log('暂停UI注入完成');
            return 'success';
          } catch (error) {
            console.error('注入UI时出错:', error);
            return 'error: ' + error.message;
          }
        })()
      `);

      console.log("暂停UI注入结果:", result);
    } catch (error) {
      console.error("注入暂停UI时出错:", error);
    }
  }

  private async saveLoginInfo(): Promise<void> {
    if (!this.loginWindow) {
      throw new Error("登录窗口未初始化");
    }

    if (!this.currentAccountId) {
      throw new Error("当前账号ID未设置");
    }

    try {
      console.log("开始保存登录信息...");

      // 获取 cookies
      const cookies = await this.loginWindow.webContents.session.cookies.get(
        {},
      );
      console.log(`获取到 ${cookies.length} 个 cookies`);

      // 获取 localStorage 和 sessionStorage
      const storageData = await this.loginWindow.webContents.executeJavaScript(`
        (() => {
          const ls = {};
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key) ls[key] = localStorage.getItem(key) || '';
          }
          const ss = {};
          for (let i = 0; i < sessionStorage.length; i++) {
            const key = sessionStorage.key(i);
            if (key) ss[key] = sessionStorage.getItem(key) || '';
          }
          return { localStorage: ls, sessionStorage: ss };
        })()
      `);

      console.log(
        `获取到 ${Object.keys(storageData.localStorage).length} 个 localStorage 项`,
      );
      console.log(
        `获取到 ${Object.keys(storageData.sessionStorage).length} 个 sessionStorage 项`,
      );

      // 获取用户代理
      const userAgent = this.loginWindow.webContents.getUserAgent();
      console.log("获取到用户代理:", `${userAgent.substring(0, 100)}...`);

      // 获取用户信息
      let userInfo = null;
      try {
        console.log("开始获取用户信息...");
        userInfo = await this.loginWindow.webContents.executeJavaScript(`
          (() => {
            try {
              // 尝试从页面中获取用户信息
              // 方法1: 从页面标题或特定元素中获取
              const nicknameElement = document.querySelector('[data-e2e="user-nickname"], .user-nickname, .nickname, [class*="nickname"], [class*="user"]');
              const avatarElement = document.querySelector('[data-e2e="user-avatar"], .user-avatar, .avatar, [class*="avatar"], img[src*="avatar"]');
              
              // 方法2: 从localStorage中获取用户信息
              const userInfoFromStorage = localStorage.getItem('userInfo') || localStorage.getItem('user_info') || localStorage.getItem('xiaohongshu_user_info');
              let userInfoData = null;
              if (userInfoFromStorage) {
                try {
                  userInfoData = JSON.parse(userInfoFromStorage);
                } catch (e) {
                  console.log('解析用户信息失败:', e);
                }
              }
              
              // 方法3: 从页面URL或全局变量中获取
              const userIdFromUrl = window.location.pathname.match(/\\/user\\/([^\\/]+)/)?.[1] || 
                                   window.location.search.match(/user_id=([^&]+)/)?.[1];
              
              // 方法4: 从页面中的script标签中查找用户信息
              const scripts = document.querySelectorAll('script');
              let userInfoFromScript = null;
              for (const script of scripts) {
                const content = script.textContent || script.innerHTML;
                if (content.includes('userInfo') || content.includes('user_info') || content.includes('nickname')) {
                  try {
                    const match = content.match(/userInfo\\s*[:=]\\s*({[^}]+})/);
                    if (match) {
                      userInfoFromScript = JSON.parse(match[1]);
                      break;
                    }
                  } catch (e) {
                    console.log('从script中解析用户信息失败:', e);
                  }
                }
              }
              
              // 组合所有可能的信息
              const nickname = nicknameElement?.textContent?.trim() || 
                              userInfoData?.nickname || 
                              userInfoData?.name || 
                              userInfoFromScript?.nickname || 
                              userInfoFromScript?.name || 
                              '未知用户';
              
              const avatar = avatarElement?.src || 
                            avatarElement?.getAttribute('data-src') || 
                            userInfoData?.avatar || 
                            userInfoData?.avatar_url || 
                            userInfoFromScript?.avatar || 
                            userInfoFromScript?.avatar_url || 
                            '';
              
              const userId = userIdFromUrl || 
                            userInfoData?.user_id || 
                            userInfoData?.id || 
                            userInfoFromScript?.user_id || 
                            userInfoFromScript?.id || 
                            '';
              
              console.log('获取到的用户信息:', { nickname, avatar, userId });
              
              return {
                nickname: nickname || '未知用户',
                avatar: avatar || '',
                userId: userId || ''
              };
            } catch (error) {
              console.error('获取用户信息时出错:', error);
              return {
                nickname: '未知用户',
                avatar: '',
                userId: ''
              };
            }
          })()
        `);
        console.log("用户信息获取结果:", userInfo);
      } catch (error) {
        console.error("获取用户信息失败:", error);
        userInfo = {
          nickname: "未知用户",
          avatar: "",
          userId: "",
        };
      }

      // 构建登录信息对象 - 使用 Playwright 兼容格式
      const loginInfo: XiaohongshuLoginInfo = {
        cookies: cookies
          .map((cookie) => {
            // 验证 domain 格式
            const validDomain = cookie.domain;

            // 如果 domain 为空或无效，跳过这个 cookie
            if (!validDomain || validDomain.length === 0) {
              console.log(`跳过无效 domain 的 cookie: ${cookie.name}`);
              return null;
            }

            // 更健壮的 sameSite 转换 - 使用 Playwright 兼容格式
            let sameSite: "Strict" | "Lax" | "None";
            const sameSiteValue = String(cookie.sameSite || "").toUpperCase();

            switch (sameSiteValue) {
              case "STRICT":
                sameSite = "Strict";
                break;
              case "LAX":
                sameSite = "Lax";
                break;
              case "NO_RESTRICTION":
              case "NONE":
                sameSite = "None";
                break;
              default:
                // 对于 unspecified，根据 secure 和 httpOnly 来决定
                if (cookie.secure) {
                  sameSite = "None";
                } else {
                  sameSite = "Lax";
                }
                break;
            }

            return {
              name: cookie.name,
              value: cookie.value,
              domain: validDomain,
              path: cookie.path || "/",
              expires: cookie.session ? -1 : cookie.expirationDate || -1,
              httpOnly: cookie.httpOnly || false,
              secure: cookie.secure || false,
              sameSite: sameSite,
            };
          })
          .filter((cookie) => cookie !== null),
        origins: [
          {
            origin: "https://creator.xiaohongshu.com",
            localStorage: Object.entries(storageData.localStorage).map(
              ([name, value]) => ({
                name,
                value: String(value),
              }),
            ),
          },
        ],
        sessionStorage: storageData.sessionStorage,
        userAgent,
        timestamp: Date.now(),
        userInfo: userInfo,
      };

      // 读取现有的多账号数据
      let accountsData: XiaohongshuAccountsData = {
        accounts: {},
        lastUpdated: Date.now(),
      };
      if (existsSync(LOGIN_INFO_PATH)) {
        try {
          const existingData = readFileSync(LOGIN_INFO_PATH, "utf8");
          const parsedData = JSON.parse(existingData);
          if (parsedData.accounts) {
            // 新格式
            accountsData = parsedData;
          }
        } catch (error) {
          console.error("读取现有数据时出错，将创建新文件:", error);
        }
      }

      // 添加或更新当前账号的登录信息
      accountsData.accounts[this.currentAccountId] = loginInfo;
      accountsData.lastUpdated = Date.now();

      await mkdir(DATA_DIR, { recursive: true });
      await writeFile(LOGIN_INFO_PATH, JSON.stringify(accountsData, null, 2));
      console.log(
        `登录信息已成功保存到: ${LOGIN_INFO_PATH}，账号ID: ${this.currentAccountId}`,
      );
    } catch (error) {
      console.error("保存登录信息时出错:", error);
      throw error;
    }
  }

  async close(): Promise<void> {
    try {
      if (this.loginWindow && !this.loginWindow.isDestroyed()) {
        this.loginWindow.close();
        this.loginWindow = null;
      }
    } catch (error) {
      console.error("关闭登录窗口时出错:", error);
    }
  }

  // 检查是否已有登录信息
  static hasLoginInfo(): boolean {
    return existsSync(LOGIN_INFO_PATH);
  }

  // 检查是否有指定账号的登录信息
  static hasAccountLoginInfo(accountId: string): boolean {
    try {
      if (!existsSync(LOGIN_INFO_PATH)) {
        return false;
      }

      const data = readFileSync(LOGIN_INFO_PATH, "utf8");
      const accountsData: XiaohongshuAccountsData = JSON.parse(data);
      return !!accountsData.accounts[accountId];
    } catch (error) {
      console.error("检查账号登录信息时出错:", error);
      return false;
    }
  }

  // 读取所有账号的登录信息
  static getAllAccountsData(): XiaohongshuAccountsData | null {
    try {
      if (!existsSync(LOGIN_INFO_PATH)) {
        return null;
      }

      const data = readFileSync(LOGIN_INFO_PATH, "utf8");
      const parsedData = JSON.parse(data);

      // 检查是否是旧格式（单个账号），如果是则迁移
      if (parsedData.cookies && Array.isArray(parsedData.cookies)) {
        // 旧格式，迁移到新格式
        console.log("检测到旧格式数据，正在迁移...");

        // 转换旧格式到新格式
        const migratedData: XiaohongshuLoginInfo = {
          cookies: parsedData.cookies,
          origins: parsedData.localStorage
            ? [
                {
                  origin: "https://creator.xiaohongshu.com",
                  localStorage: Object.entries(parsedData.localStorage).map(
                    ([name, value]) => ({
                      name,
                      value: String(value),
                    }),
                  ),
                },
              ]
            : [],
          sessionStorage: parsedData.sessionStorage || {},
          userAgent: parsedData.userAgent || "",
          timestamp: parsedData.timestamp || Date.now(),
          userInfo: parsedData.userInfo,
        };

        const accountsData: XiaohongshuAccountsData = {
          accounts: {
            xiaohongshu_legacy: migratedData,
          },
          lastUpdated: Date.now(),
        };

        // 保存迁移后的数据
        writeFile(LOGIN_INFO_PATH, JSON.stringify(accountsData, null, 2));
        return accountsData;
      } else if (parsedData.accounts) {
        // 新格式
        return parsedData;
      }

      return null;
    } catch (error) {
      console.error("读取登录信息时出错:", error);
      return null;
    }
  }

  // 读取指定账号的登录信息
  static getAccountLoginInfo(accountId: string): XiaohongshuLoginInfo | null {
    try {
      const accountsData = XiaohongshuLoginService.getAllAccountsData();
      if (!accountsData || !accountsData.accounts[accountId]) {
        return null;
      }
      return accountsData.accounts[accountId];
    } catch (error) {
      console.error("读取账号登录信息时出错:", error);
      return null;
    }
  }

  // 读取登录信息（向后兼容，返回第一个账号的信息）
  static getLoginInfo(): XiaohongshuLoginInfo | null {
    try {
      const accountsData = XiaohongshuLoginService.getAllAccountsData();
      if (!accountsData) {
        return null;
      }

      // 返回第一个账号的信息
      const accountIds = Object.keys(accountsData.accounts);
      if (accountIds.length === 0) {
        return null;
      }

      return accountsData.accounts[accountIds[0]];
    } catch (error) {
      console.error("读取登录信息时出错:", error);
      return null;
    }
  }

  // 删除指定账号的登录信息
  static deleteAccountLoginInfo(accountId: string): boolean {
    try {
      const accountsData = XiaohongshuLoginService.getAllAccountsData();
      if (!accountsData) {
        return false;
      }

      if (accountsData.accounts[accountId]) {
        delete accountsData.accounts[accountId];
        accountsData.lastUpdated = Date.now();

        writeFile(LOGIN_INFO_PATH, JSON.stringify(accountsData, null, 2));
        console.log(`已删除账号 ${accountId} 的登录信息`);
        return true;
      }

      return false;
    } catch (error) {
      console.error("删除账号登录信息时出错:", error);
      return false;
    }
  }

  // 获取登录文件的路径
  static getLoginFilePath(): string {
    return LOGIN_INFO_PATH;
  }

  // 打开指定账号的浏览器窗口并自动填入登录信息
  async openAccount(accountId: string): Promise<void> {
    try {
      console.log(`正在打开账号 ${accountId} 的浏览器窗口...`);

      // 获取账号的登录信息
      const loginInfo = XiaohongshuLoginService.getAccountLoginInfo(accountId);
      if (!loginInfo) {
        throw new Error(`账号 ${accountId} 的登录信息不存在`);
      }

      // 创建新的浏览器窗口
      this.loginWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        title: `小红书账号 ${accountId}`,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
          preload: join(__dirname, "login-preload.js"),
        },
        show: false, // 先不显示，等加载完成再显示
      });

      // 设置用户代理
      this.loginWindow.webContents.setUserAgent(loginInfo.userAgent);

      // 监听页面加载完成
      this.loginWindow.webContents.on("did-finish-load", async () => {
        console.log("页面加载完成，准备填入登录信息...");

        try {
          // 设置 cookies
          for (const cookie of loginInfo.cookies) {
            try {
              // 验证 domain 格式
              let validDomain = cookie.domain;

              // 如果 domain 以点开头，去掉点
              if (validDomain.startsWith(".")) {
                validDomain = validDomain.substring(1);
              }

              // 如果 domain 为空或无效，跳过这个 cookie
              if (!validDomain || validDomain.length === 0) {
                console.log(`跳过无效 domain 的 cookie: ${cookie.name}`);
                continue;
              }

              // 构建 URL
              const url = `https://${validDomain}${cookie.path || "/"}`;

              console.log("url: ", url);

              await this.loginWindow!.webContents.session.cookies.set({
                url: url,
                name: cookie.name,
                value: cookie.value,
                domain: validDomain,
                path: cookie.path || "/",
                expirationDate:
                  cookie.expires === -1 ? undefined : cookie.expires,
                httpOnly: cookie.httpOnly,
                secure: cookie.secure,
                sameSite:
                  cookie.sameSite === "Strict"
                    ? "strict"
                    : cookie.sameSite === "Lax"
                      ? "lax"
                      : cookie.sameSite === "None"
                        ? "no_restriction"
                        : "unspecified",
              });
            } catch (cookieError) {
              console.error(`设置 cookie ${cookie.name} 失败:`, cookieError);
              // 继续设置其他 cookie，不中断整个流程
            }
          }

          // 设置 localStorage 和 sessionStorage
          await this.loginWindow!.webContents.executeJavaScript(`
            (() => {
              try {
                // 设置 localStorage
                ${
                  loginInfo.origins
                    .find(
                      (origin) =>
                        origin.origin === "https://creator.xiaohongshu.com",
                    )
                    ?.localStorage.map(
                      (item) =>
                        `localStorage.setItem('${item.name}', '${String(item.value).replace(/'/g, "\\'")}');`,
                    )
                    .join("\n") || ""
                }
                
                // 设置 sessionStorage
                ${Object.entries(loginInfo.sessionStorage)
                  .map(
                    ([key, value]) =>
                      `sessionStorage.setItem('${key}', '${String(value).replace(/'/g, "\\'")}');`,
                  )
                  .join("\n")}
                
                console.log('登录信息已填入');
                return 'success';
              } catch (error) {
                console.error('填入登录信息时出错:', error);
                return 'error: ' + error.message;
              }
            })()
          `);

          // 显示窗口
          if (this.loginWindow && !this.loginWindow.isDestroyed()) {
            this.loginWindow.show();
            console.log("浏览器窗口已显示，登录信息已填入");
          }
        } catch (error) {
          console.error("填入登录信息时出错:", error);
          if (this.loginWindow && !this.loginWindow.isDestroyed()) {
            this.loginWindow.show();
          }
        }
      });

      // 监听窗口关闭
      this.loginWindow.on("closed", () => {
        console.log("账号浏览器窗口被关闭");
        this.loginWindow = null;
      });

      // 监听页面加载错误
      this.loginWindow.webContents.on(
        "did-fail-load",
        (event, errorCode, errorDescription) => {
          console.error("页面加载失败:", errorCode, errorDescription);
        },
      );

      // 导航到小红书创作者平台
      console.log("开始导航到小红书创作者平台...");
      await this.loginWindow.loadURL("https://creator.xiaohongshu.com/");
      console.log("导航完成");
    } catch (error) {
      console.error("打开账号浏览器窗口时出错:", error);
      throw error;
    }
  }

  // 创建单个账号的登录信息文件并提交绑定
  static async createSingleAccountFileAndBind(
    accountId: string,
    platform: string,
    userId: string = "37",
    enUserName: string = "wangkt",
  ): Promise<any> {
    try {
      console.log(`开始为账号 ${accountId} 创建绑定文件...`);

      // 获取指定账号的登录信息
      const accountInfo =
        XiaohongshuLoginService.getAccountLoginInfo(accountId);
      if (!accountInfo) {
        throw new Error(`账号 ${accountId} 的登录信息不存在`);
      }

      // 直接使用账号信息，不包含外层结构
      const tempLoginData = accountInfo;

      // 将当前账号信息写入临时文件
      const tempFilePath = join(DATA_DIR, `${platform}_temp_${accountId}.json`);
      await writeFile(tempFilePath, JSON.stringify(tempLoginData, null, 2));

      console.log(`临时文件已创建: ${tempFilePath}`);

      // 创建FormData并提交
      const fileData = createReadStream(tempFilePath);
      const formData = new FormData();
      formData.append("file", fileData);
      formData.append("user_id", userId);
      formData.append("en_user_name", enUserName);
      formData.append("platform", platform);

      const userBind = await instance({
        url: "/svr/bind/bind_user",
        method: "POST",
        data: formData,
        headers: formData.getHeaders
          ? formData.getHeaders()
          : { "content-type": "multipart/form-data" },
      });

      console.log("绑定请求成功:", userBind);

      // 删除临时文件
      try {
        // await unlink(tempFilePath);
        console.log("临时文件已删除:", tempFilePath);
      } catch (error) {
        console.warn("删除临时文件失败:", error);
      }

      return userBind;
    } catch (error) {
      console.error("创建绑定文件失败:", error);
      throw error;
    }
  }
}
