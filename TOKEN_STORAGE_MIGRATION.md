# Token Storage Migration: localStorage → electron-store

## 概述

本项目已将token存储从浏览器的localStorage迁移到electron-store，以提供更好的安全性和持久性。由于所有请求都在主进程中进行，token操作直接使用electron-store，无需IPC通信。

## 主要变更

### 1. 新增文件

- `src/lib/tokenStore.ts` - electron-store token存储服务
- `src/lib/tokenUtils.ts` - token管理工具类

### 2. 修改文件

- `src/main.ts` - 添加IPC处理器（用于渲染进程访问）
- `src/preload.ts` - 暴露token API（用于渲染进程访问）
- `src/types.d.ts` - 添加类型定义
- `src/lib/axios.ts` - 更新token获取和存储逻辑

## 新API

### 主进程 (Main Process) - 推荐使用

```typescript
import { tokenStore } from './lib/tokenStore';

// 设置token
tokenStore.setAccessToken(token, rememberMe);

// 获取token
const token = tokenStore.getAccessToken();

// 清除token
tokenStore.clearAccessToken();

// 检查是否有token
const hasToken = tokenStore.hasAccessToken();
```

### 通过axios导出的函数

```typescript
import { setAccessToken, getAccessToken, clearAccessToken } from './lib/axios';

// 设置token
setAccessToken(token, rememberMe);

// 获取token
const token = getAccessToken();

// 清除token
clearAccessToken();
```

### 渲染进程 (Renderer Process) - 通过IPC

```typescript
// 设置token
await window.electronAPI.token.set(token, rememberMe);

// 获取token
const token = await window.electronAPI.token.get();

// 清除token
await window.electronAPI.token.clear();

// 检查是否有token
const hasToken = await window.electronAPI.token.has();
```

### 工具类

```typescript
import { TokenUtils } from './lib/tokenUtils';

// 登录
TokenUtils.login('your_access_token', true);

// 检查登录状态
const isLoggedIn = TokenUtils.isLoggedIn();

// 获取当前token
const token = TokenUtils.getCurrentToken();

// 登出
TokenUtils.logout();
```

## 优势

### 1. 安全性
- token存储在本地文件系统，而不是浏览器存储
- 不受浏览器清除数据影响
- 更好的数据隔离

### 2. 持久性
- 应用重启后token仍然存在
- 支持"记住我"功能
- 数据持久化到磁盘

### 3. 性能
- 主进程中直接访问tokenStore，无需IPC通信
- 同步操作，性能更好
- 简化的代码结构

## 数据存储位置

electron-store将数据存储在以下位置：

- **macOS**: `~/Library/Application Support/yc-media-platform/tokens.json`
- **Windows**: `%APPDATA%\yc-media-platform\tokens.json`
- **Linux**: `~/.config/yc-media-platform\tokens.json`

## 迁移指南

### 现有代码更新

如果你的代码中直接使用了localStorage存储token，需要更新为新的API：

```typescript
// 旧代码
localStorage.setItem('access-token', token);
const token = localStorage.getItem('access-token');
localStorage.removeItem('access-token');

// 新代码（主进程）
import { setAccessToken, getAccessToken, clearAccessToken } from './lib/axios';

setAccessToken(token, rememberMe);
const token = getAccessToken();
clearAccessToken();
```

### 同步操作

新的token API在主进程中都是同步的，使用更简单：

```typescript
// 主进程中使用
try {
  setAccessToken(token, true);
  console.log('Token saved successfully');
} catch (error) {
  console.error('Failed to save token:', error);
}
```

## 错误处理

所有token操作都包含错误处理：

```typescript
// 主进程中直接使用tokenStore
import { tokenStore } from './lib/tokenStore';

try {
  tokenStore.setAccessToken(token, rememberMe);
} catch (error) {
  console.error('Token操作失败:', error);
}
```

## 测试

可以通过以下方式测试新的token存储系统：

```typescript
// 测试token存储
TokenUtils.login('test_token', true);
const isLoggedIn = TokenUtils.isLoggedIn();
console.log('Is logged in:', isLoggedIn); // true

// 测试token获取
const token = TokenUtils.getCurrentToken();
console.log('Current token:', token); // 'test_token'

// 测试token清除
TokenUtils.logout();
const isStillLoggedIn = TokenUtils.isLoggedIn();
console.log('Is still logged in:', isStillLoggedIn); // false
```

## 注意事项

1. **主进程优先**: 在主进程中直接使用tokenStore，性能更好
2. **同步操作**: 主进程中的token操作都是同步的，无需await
3. **IPC通信**: 渲染进程需要通过IPC访问token，操作是异步的
4. **数据迁移**: 现有localStorage中的token不会自动迁移，需要重新登录

## 依赖

确保package.json中包含electron-store依赖：

```json
{
  "dependencies": {
    "electron-store": "^10.1.0"
  }
}
```

## 架构说明

```
主进程 (Main Process)
├── tokenStore (electron-store)
├── axios拦截器 (直接访问tokenStore)
└── IPC处理器 (供渲染进程使用)

渲染进程 (Renderer Process)
└── 通过IPC访问token API
```

这种架构确保了：
- 主进程中的请求直接使用tokenStore，性能最优
- 渲染进程可以通过IPC访问token，保持灵活性
- 代码结构清晰，职责分离明确 