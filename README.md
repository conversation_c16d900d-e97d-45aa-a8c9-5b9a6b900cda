# YC Media Platform

一个基于 Electron 的跨平台媒体管理工具，支持多平台账号管理和内容发布。

## 功能特性

### 账号管理
- **多平台支持**: 支持抖音、快手、小红书等主流社交媒体平台
- **扫码登录**: 通过扫码方式安全登录各平台账号
- **用户信息获取**: 登录后自动获取用户昵称、头像和用户ID
- **多账号管理**: 支持管理多个平台账号，每个账号独立存储
- **登录状态保持**: 自动保存登录信息，无需重复登录
- **精确绑定**: 支持单个账号的精确绑定，只传递必要的登录信息

### 内容发布
- 支持多平台内容发布
- 批量发布功能
- 发布任务管理
- **内容预览**: 发布前预览内容效果
- **发布状态跟踪**: 实时跟踪发布进度和状态

### 数据统计
- 平台数据统计
- 内容表现分析
- 趋势图表展示
- **数据可视化**: 直观展示各平台数据表现
- **趋势分析**: 分析内容发布趋势和效果

## 用户信息获取功能

### 功能说明
在扫码登录成功后，系统会自动尝试获取登录用户的基本信息，包括：

- **用户昵称**: 显示用户的昵称或用户名
- **用户头像**: 获取用户的头像图片
- **用户ID**: 获取用户的唯一标识符

### 获取方式
系统采用多种方式尝试获取用户信息：

1. **页面元素解析**: 从页面DOM元素中提取用户信息
2. **本地存储读取**: 从localStorage中读取缓存的用户信息
3. **URL参数解析**: 从页面URL中提取用户ID
4. **脚本标签解析**: 从页面脚本中查找用户信息

### 显示效果
- 在账号列表中显示用户头像和昵称
- 如果获取失败，会显示默认的"未知用户"标识
- 在控制台输出详细的获取过程日志

### 技术实现
- 使用Electron的webContents.executeJavaScript方法执行页面脚本
- 支持多种选择器模式，提高信息获取成功率
- 错误处理机制，确保获取失败时不影响登录流程

## 快速开始

### 安装依赖
```bash
npm install
```

### 启动应用
```bash
npm start
```

### 打包应用
```bash
npm run make
```

## 使用指南

### 账号管理

#### 添加新账号
1. 打开应用，进入"账号管理"页面
2. 点击"添加账号"按钮
3. 选择要添加的平台（抖音、快手、小红书）
4. 在弹出的登录窗口中完成扫码登录
5. 登录成功后，账号信息会自动保存

#### 管理现有账号
在"账号管理"页面，你可以看到所有已保存的账号列表：

- **重新登录**: 点击"重新登录"按钮，可以重新进行登录流程
- **打开账号**: 点击"打开"按钮，会打开一个新的浏览器窗口，并自动填入该账号的登录信息，直接进入平台管理页面
- **删除账号**: 点击"删除"按钮，可以删除不需要的账号

#### 抖音账号"打开"功能
这是一个特别实用的功能：

1. 在抖音账号列表中，点击任意账号的"打开"按钮
2. 系统会自动打开一个新的浏览器窗口
3. 窗口会自动填入该账号的所有登录信息（cookies、localStorage、sessionStorage）
4. 页面加载完成后，你会直接进入抖音创作者平台，无需重新登录
5. 可以正常使用所有平台功能，就像在普通浏览器中登录一样

这个功能特别适合：
- 快速查看账号状态
- 进行内容管理
- 查看数据统计
- 进行平台操作

### 注意事项
- 确保在登录过程中不要手动关闭登录窗口
- 登录信息会安全保存在本地，不会上传到服务器
- 如果登录失败，请检查网络连接并重试

## 开发说明

### 项目结构
```
src/
├── components/     # 通用组件
├── config/        # 配置文件
├── lib/           # 核心库文件
├── pages/         # 页面组件
├── store/         # 状态管理
├── types/         # 类型定义
└── main.ts        # 主进程入口
```

### 技术栈
- **前端**: React + TypeScript + Ant Design
- **桌面端**: Electron
- **构建工具**: Vite + Electron Forge
- **代码规范**: Biome

## 故障排除

如果遇到问题，请查看 [TROUBLESHOOTING.md](./TROUBLESHOOTING.md) 文件。

## 许可证

MIT License 