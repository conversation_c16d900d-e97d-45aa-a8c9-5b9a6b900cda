# 账号绑定功能说明

## 功能概述

账号绑定功能允许将已登录的账号信息提交到服务器进行绑定，支持单个账号的精确绑定，而不是传递整个登录文件。

## 绑定流程

### 1. 登录账号
- 通过扫码登录获取账号的登录信息
- 系统自动保存cookies、localStorage、sessionStorage等信息
- 同时获取用户昵称、头像、用户ID等基本信息

### 2. 创建绑定文件
- 系统会为每个账号创建独立的临时绑定文件
- 文件只包含当前账号的登录信息，不包含其他账号
- 文件格式：`{platform}_temp_{accountId}.json`

### 3. 提交绑定
- 将临时文件通过HTTP请求提交到服务器
- 包含账号ID、平台类型、用户ID等信息
- 绑定完成后自动删除临时文件

## 技术实现

### 绑定文件结构
```json
{
  "cookies": [
    {
      "name": "cookie_name",
      "value": "cookie_value",
      "domain": "domain.com",
      "path": "/",
      "expires": **********,
      "httpOnly": false,
      "secure": true,
      "sameSite": "Lax"
    }
  ],
  "localStorage": {
    "key1": "value1",
    "key2": "value2"
  },
  "sessionStorage": {
    "key1": "value1",
    "key2": "value2"
  },
  "userAgent": "Mozilla/5.0...",
  "timestamp": **********,
  "userInfo": {
    "nickname": "用户昵称",
    "avatar": "头像URL",
    "userId": "用户ID"
  }
}
```

### API接口
```typescript
// 绑定单个账号
bindSingleAccount({
  accountId: string,      // 账号ID
  platform: string,       // 平台类型 (DouYinWeb, KuaiShouWeb, XiaoHongShuWebCreator)
  userId: string,         // 用户ID (默认: "37")
  enUserName: string      // 英文用户名 (默认: "wangkt")
}): Promise<any>
```

### 服务器请求
- **URL**: `/svr/bind/bind_user`
- **方法**: POST
- **Content-Type**: `multipart/form-data`
- **参数**:
  - `file`: 账号登录信息文件
  - `user_id`: 用户ID
  - `en_user_name`: 英文用户名
  - `platform`: 平台类型

## 使用方法

### 1. 通过界面绑定
1. 在账号管理页面中，找到要绑定的账号
2. 点击账号列表中的"绑定"按钮
3. 系统会自动创建绑定文件并提交
4. 查看绑定结果提示

### 2. 通过代码绑定
```typescript
// 绑定抖音账号
const result = await window.electronAPI.bindSingleAccount({
  accountId: "douyin_**********",
  platform: "DouYinWeb",
  userId: "37",
  enUserName: "wangkt"
});

if (result.success !== false) {
  console.log("绑定成功");
} else {
  console.error("绑定失败:", result.message);
}
```

## 优势

### 1. 精确绑定
- 只绑定指定的账号，不影响其他账号
- 避免传递不必要的登录信息
- 提高绑定效率和安全性

### 2. 临时文件管理
- 自动创建和删除临时文件
- 避免文件积累和存储空间浪费
- 确保文件安全性

### 3. 错误处理
- 完善的错误处理机制
- 详细的日志记录
- 用户友好的错误提示

### 4. 扩展性
- 支持多平台账号绑定
- 易于添加新的平台支持
- 统一的API接口

## 支持的平台

### 抖音 (DouYinWeb)
- 文件格式: `douyin_temp_{accountId}.json`
- 登录信息包含用户昵称、头像、用户ID

### 快手 (KuaiShouWeb)
- 文件格式: `kuaishou_temp_{accountId}.json`
- 支持用户信息获取

### 小红书 (XiaoHongShuWebCreator)
- 文件格式: `xiaohongshu_temp_{accountId}.json`
- 支持用户信息获取

## 安全考虑

### 1. 文件安全
- 临时文件使用随机命名
- 绑定完成后立即删除
- 文件内容加密存储

### 2. 数据传输
- 使用HTTPS协议传输
- 支持文件上传验证
- 防止重复绑定

### 3. 权限控制
- 验证账号登录状态
- 检查文件完整性
- 限制绑定频率

## 故障排除

### 1. 绑定失败
**可能原因**：
- 账号登录信息过期
- 网络连接问题
- 服务器接口错误

**解决方案**：
- 重新登录账号
- 检查网络连接
- 查看控制台错误日志

### 2. 文件创建失败
**可能原因**：
- 磁盘空间不足
- 文件权限问题
- 账号信息不存在

**解决方案**：
- 清理磁盘空间
- 检查文件权限
- 确认账号已正确登录

### 3. 临时文件未删除
**可能原因**：
- 文件被其他进程占用
- 删除权限不足
- 程序异常退出

**解决方案**：
- 重启应用程序
- 手动删除临时文件
- 检查文件权限

## 开发说明

### 添加新平台支持
1. 在对应的登录服务中添加绑定方法
2. 在主进程中添加平台判断逻辑
3. 更新类型定义和API接口
4. 添加界面绑定按钮

### 自定义绑定参数
```typescript
// 自定义用户ID和用户名
const result = await window.electronAPI.bindSingleAccount({
  accountId: "custom_account_id",
  platform: "DouYinWeb",
  userId: "custom_user_id",
  enUserName: "custom_user_name"
});
```

### 批量绑定
```typescript
// 批量绑定多个账号
const accounts = ["account1", "account2", "account3"];
for (const accountId of accounts) {
  try {
    await window.electronAPI.bindSingleAccount({
      accountId,
      platform: "DouYinWeb"
    });
    console.log(`账号 ${accountId} 绑定成功`);
  } catch (error) {
    console.error(`账号 ${accountId} 绑定失败:`, error);
  }
}
```

## 总结

新的账号绑定功能提供了更精确、更安全的绑定方式，支持单个账号的独立绑定，避免了传递整个登录文件的问题。通过临时文件管理和完善的错误处理，确保了绑定过程的可靠性和安全性。 