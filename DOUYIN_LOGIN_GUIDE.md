# 抖音登录功能使用指南

## 功能概述

本功能允许用户通过扫码登录抖音创作者平台，并自动保存登录信息到本地文件中，方便后续使用。

## 使用步骤

### 1. 启动应用
```bash
npm start
```

### 2. 进入账号管理页面
在应用主界面中，点击"账号管理"标签页。

### 3. 开始登录流程
1. 点击右上角的"授权账号"按钮
2. 在弹出的对话框中选择"抖音"
3. 系统会自动打开一个新的浏览器窗口，导航到抖音创作者平台

### 4. 扫码登录
1. 在打开的浏览器窗口中，你会看到抖音创作者平台的登录页面
2. 使用手机抖音 App 扫描页面上的二维码
3. 在手机上确认登录

### 5. 自动保存
- 登录成功后，系统会自动检测登录状态
- 登录信息（包括 cookies、localStorage、sessionStorage 等）会自动保存到 `data/douyin.json` 文件中
- 浏览器窗口会自动关闭

### 6. 验证登录状态
- 登录成功后，在账号列表中会显示"抖音账号"卡片
- 抖音按钮上会显示"已登录"状态

## 技术细节

### 登录信息存储
登录信息包含以下内容：
- **Cookies**: 网站的身份验证信息
- **localStorage**: 本地存储的用户偏好设置
- **sessionStorage**: 会话存储的临时数据
- **User Agent**: 浏览器标识信息
- **时间戳**: 登录时间

### 文件位置
登录信息保存在项目根目录的 `data/douyin.json` 文件中。

### 安全注意事项
- 登录信息包含敏感数据，请妥善保管
- 不要将 `data/douyin.json` 文件分享给他人
- 该文件已被添加到 `.gitignore` 中，不会被提交到版本控制系统

## 故障排除

### 常见问题

1. **浏览器无法打开**
   - 确保已安装 Playwright：`npx playwright install chromium`
   - 检查网络连接是否正常

2. **登录超时**
   - 确保在 5 分钟内完成扫码登录
   - 检查手机网络连接
   - 重新尝试登录流程

3. **登录信息未保存**
   - 检查 `data` 目录是否存在
   - 确保应用有写入文件的权限
   - 查看控制台错误信息

4. **登录状态检测失败**
   - 可能是抖音网站结构发生变化
   - 需要更新登录检测逻辑

### 调试方法

1. **查看控制台日志**
   - 在应用中按 F12 打开开发者工具
   - 查看 Console 标签页的错误信息

2. **检查文件系统**
   - 确认 `data/douyin.json` 文件是否存在
   - 检查文件内容是否完整

3. **重新安装依赖**
   ```bash
   npm install
   npx playwright install chromium
   ```

## 开发说明

### 代码结构
- `src/lib/douyin-login.ts`: 抖音登录服务
- `src/pages/account.tsx`: 账号管理页面
- `src/main.ts`: 主进程 IPC 处理器
- `src/preload.ts`: 预加载脚本

### 自定义配置
可以在 `src/lib/douyin-login.ts` 中修改以下配置：
- 登录超时时间（默认 5 分钟）
- 登录检测逻辑
- 文件保存路径

### 扩展功能
可以基于保存的登录信息实现：
- 自动发布内容
- 数据统计获取
- 账号信息同步
- 多账号管理

## 更新日志

### v1.0.0
- 初始版本
- 支持抖音创作者平台扫码登录
- 自动保存登录信息
- 登录状态检测和显示 