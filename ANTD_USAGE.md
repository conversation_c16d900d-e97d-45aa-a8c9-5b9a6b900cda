# Ant Design 使用指南

## 已安装的依赖

- `antd`: Ant Design 主包 (v5.26.1)
- `@ant-design/icons`: Ant Design 图标包 (v6.0.0)

## 基本使用

### 1. 导入组件

```tsx
import { Button, Card, Space, Typography, Row, Col } from 'antd';
import { UserOutlined, VideoCameraOutlined } from '@ant-design/icons';
```

### 2. 使用组件

```tsx
import React from 'react';
import { Button, Card, Space } from 'antd';
import { UserOutlined } from '@ant-design/icons';

export default function MyComponent() {
  return (
    <Card title="示例卡片">
      <Space direction="vertical">
        <Button type="primary" icon={<UserOutlined />}>
          主要按钮
        </Button>
        <Button>默认按钮</Button>
      </Space>
    </Card>
  );
}
```

## 常用组件示例

### 按钮 (Button)
```tsx
<Button type="primary">主要按钮</Button>
<Button type="default">默认按钮</Button>
<Button type="dashed">虚线按钮</Button>
<Button type="text">文本按钮</Button>
<Button type="link">链接按钮</Button>
```

### 卡片 (Card)
```tsx
<Card title="卡片标题" extra={<a href="#">更多</a>}>
  <p>卡片内容</p>
</Card>
```

### 统计数值 (Statistic)
```tsx
<Statistic
  title="活跃用户"
  value={112893}
  prefix={<UserOutlined />}
  valueStyle={{ color: '#3f8600' }}
/>
```

### 进度条 (Progress)
```tsx
<Progress percent={70} status="active" />
```

### 栅格布局 (Row/Col)
```tsx
<Row gutter={[16, 16]}>
  <Col xs={24} sm={12} lg={6}>
    <Card>内容1</Card>
  </Col>
  <Col xs={24} sm={12} lg={6}>
    <Card>内容2</Card>
  </Col>
</Row>
```

### 图标 (Icons)
```tsx
import { 
  UserOutlined, 
  VideoCameraOutlined, 
  BarChartOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  LikeOutlined,
  ShareAltOutlined
} from '@ant-design/icons';

// 在组件中使用
<UserOutlined />
<VideoCameraOutlined />
```

## 样式配置

Ant Design 的样式已经通过 `antd/dist/reset.css` 导入到全局样式中，确保组件样式正常显示。

## 主题定制

如果需要定制主题，可以在 `src/styles/globals.css` 中添加 Ant Design 的主题变量：

```css
:root {
  --ant-primary-color: #1890ff;
  --ant-primary-color-hover: #40a9ff;
  --ant-primary-color-active: #096dd9;
}
```

## 注意事项

1. Ant Design 与现有的 Tailwind CSS 可以很好地配合使用
2. 组件的 className 属性可以与 Tailwind 类名结合使用
3. 响应式设计可以使用 Ant Design 的栅格系统或 Tailwind 的响应式类名

## 更多资源

- [Ant Design 官方文档](https://ant.design/components/overview/)
- [Ant Design Icons](https://ant.design/components/icon/)
- [Ant Design 主题定制](https://ant.design/docs/react/customize-theme) 