# 抖音登录故障排除指南

## 问题：登录信息没有保存到 douyin.json 文件

### 新的登录流程

我们使用 Electron 内置浏览器窗口来实现登录功能，这样可以获取到完整的登录信息：

1. **点击"抖音"按钮** - 打开 Electron 内置浏览器窗口
2. **查看状态提示** - 窗口右上角会显示登录状态和倒计时
3. **扫码登录** - 在窗口中完成扫码登录
4. **状态验证** - 系统会验证登录状态，确保真正登录成功
5. **自动保存** - 登录成功后自动检测并保存登录信息
6. **窗口关闭** - 保存完成后自动关闭登录窗口

### 状态提示说明

登录窗口右上角会显示以下状态：

- **"请扫码登录... (5:00)"** - 等待扫码，括号内是倒计时
- **"检测登录状态..."** - 正在验证登录状态
- **"登录成功！正在保存信息..."** - 登录成功，正在保存
- **"登录超时，请重新开始"** - 5分钟超时，需要重新开始

### 可能的原因和解决方案

#### 1. 登录窗口没有打开
**症状**: 点击抖音按钮后，没有看到新的浏览器窗口

**解决方案**:
1. 检查控制台是否有错误信息
2. 确保应用有创建窗口的权限
3. 重新启动应用

#### 2. 登录检测失败
**症状**: 扫码登录后，窗口没有自动关闭，或者没有生成 douyin.json 文件

**解决方案**:
1. 确保在扫码登录后，等待页面完全加载（通常需要 3-5 秒）
2. 不要手动关闭登录窗口
3. 检查控制台日志，查看登录检测过程

#### 3. 手动保存功能
如果自动保存失败，可以使用手动保存：

1. 点击"授权账号"按钮
2. 点击"抖音"按钮，等待登录窗口打开
3. 完成扫码登录
4. **不要关闭登录窗口**
5. 点击"手动保存登录信息（测试）"按钮
6. 如果成功，会显示"手动保存成功！"的提示

#### 4. 检查控制台日志
如果问题仍然存在，请检查控制台日志：

1. 在应用中按 F12 打开开发者工具
2. 查看 Console 标签页
3. 寻找以下日志信息：
   - "收到抖音登录请求..."
   - "正在创建登录窗口..."
   - "登录窗口已显示"
   - "已打开抖音创作者平台，请扫码登录..."
   - "检测到登录成功！"
   - "开始保存登录信息..."
   - "登录信息已成功保存到: ..."

#### 5. 文件权限问题
确保应用有写入文件的权限：

```bash
# 检查 data 目录权限
ls -la data/

# 如果需要，修改权限
chmod 755 data/
```

#### 6. 网络连接问题
确保网络连接正常，能够访问抖音创作者平台：

1. 在浏览器中手动访问 https://creator.douyin.com/
2. 确认页面能够正常加载
3. 确认能够正常扫码登录

### 调试步骤

#### 步骤 1: 基本测试
1. 启动应用
2. 进入"账号管理"页面
3. 点击"授权账号" → "抖音"
4. 确认登录窗口打开并导航到抖音创作者平台
5. 完成扫码登录
6. 等待自动保存或使用手动保存

#### 步骤 2: 检查文件
```bash
# 检查 data 目录是否存在
ls -la data/

# 检查 douyin.json 文件是否存在
ls -la data/douyin.json

# 查看文件内容（如果存在）
cat data/douyin.json
```

#### 步骤 3: 查看日志
在应用的控制台中查看详细的日志信息，特别关注：
- 登录窗口创建过程
- 登录检测过程
- 文件保存过程
- 错误信息

### 常见错误信息

#### "创建登录窗口时出错"
- 原因：Electron 窗口创建失败
- 解决：重新启动应用，检查系统资源

#### "登录窗口未初始化"
- 原因：登录窗口没有正确创建
- 解决：重新点击"抖音"按钮

#### "检测到登录成功！"但没有保存
- 原因：保存过程中出现错误
- 解决：使用手动保存功能

#### "保存登录信息时出错"
- 原因：文件写入权限问题或磁盘空间不足
- 解决：检查文件权限和磁盘空间

### 新的工作流程

1. **启动应用** → 进入"账号管理"
2. **点击"授权账号"** → 选择"抖音"
3. **Electron 窗口打开** → 导航到抖音创作者平台
4. **扫码登录** → 在窗口中完成登录
5. **自动检测** → 系统自动检测登录状态
6. **自动保存** → 登录信息自动保存到 douyin.json
7. **窗口关闭** → 保存完成后自动关闭
8. **验证成功** → 查看账号列表中的抖音账号卡片

### 技术优势

- **完整信息获取** - 可以获取 cookies、localStorage、sessionStorage
- **自动检测** - 无需手动干预，自动检测登录状态
- **安全可靠** - 使用 Electron 内置浏览器，避免兼容性问题
- **用户体验好** - 无缝集成，操作简单

### 联系支持

如果以上方法都无法解决问题，请提供以下信息：
1. 操作系统版本
2. 应用版本
3. 控制台错误日志
4. 具体的操作步骤
5. 网络环境信息
6. 登录窗口是否正常打开 